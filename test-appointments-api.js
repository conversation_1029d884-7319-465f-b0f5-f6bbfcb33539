// Test script for appointments API
const testAppointmentsAPI = async () => {
  try {
    // First login to get token
    const loginResponse = await fetch('http://localhost:3002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    
    if (!loginData.success) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginData.token;
    console.log('✅ Login successful');

    // Test getting all appointments
    console.log('\n📋 Testing GET /api/appointments');
    const appointmentsResponse = await fetch('http://localhost:3002/api/appointments', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const appointmentsData = await appointmentsResponse.json();
    console.log('Appointments Response:', JSON.stringify(appointmentsData, null, 2));

    // Test getting available slots
    if (appointmentsData.success && appointmentsData.data.length > 0) {
      const doctorId = appointmentsData.data[0].doctor._id;
      const today = new Date().toISOString().split('T')[0];
      
      console.log(`\n🕐 Testing GET /api/appointments/available-slots?doctorId=${doctorId}&date=${today}`);
      
      const slotsResponse = await fetch(`http://localhost:3002/api/appointments/available-slots?doctorId=${doctorId}&date=${today}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const slotsData = await slotsResponse.json();
      console.log('Available Slots Response:', JSON.stringify(slotsData, null, 2));
    }

  } catch (error) {
    console.error('Error:', error);
  }
};

testAppointmentsAPI();
