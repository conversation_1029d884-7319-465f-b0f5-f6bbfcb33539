// Simple test script to test login API
const testLogin = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const data = await response.json();
    console.log('Login Response:', JSON.stringify(data, null, 2));

    if (data.success && data.token) {
      console.log('\n✅ Login successful!');
      console.log('Token:', data.token);
      
      // Test token verification
      const verifyResponse = await fetch('http://localhost:3001/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${data.token}`
        }
      });

      const verifyData = await verifyResponse.json();
      console.log('\nVerify Response:', JSON.stringify(verifyData, null, 2));
    } else {
      console.log('❌ Login failed');
    }
  } catch (error) {
    console.error('Error:', error);
  }
};

testLogin();
