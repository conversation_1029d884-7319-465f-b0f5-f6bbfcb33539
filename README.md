# 🏥 Hospital Management System (HMS)

A comprehensive, full-stack Hospital Management System built with modern web technologies. This system provides complete end-to-end functionality for managing all aspects of hospital operations.

## 🌟 Features

### 🔐 Authentication & Authorization
- **Secure Login System** with JWT tokens
- **Role-based Access Control** (RBAC)
- **Multi-level Permissions** system
- **Password Reset** functionality
- **Session Management**

### 👥 Patient Management
- **Complete Patient Records** with medical history
- **Patient Registration** and profile management
- **Search and Filter** capabilities
- **Patient Demographics** and analytics
- **Insurance Information** management

### 📅 Appointment & Scheduling
- **Appointment Booking** system
- **Doctor Schedule** management
- **Available Time Slots** checking
- **Appointment Status** tracking
- **Conflict Detection** and prevention

### 🏥 Clinical Management
- **Electronic Medical Records** (EMR)
- **Vital Signs** tracking
- **Diagnosis** and treatment records
- **Prescription** management
- **Medical History** tracking

### 🧪 Laboratory Management
- **Lab Test Orders** and tracking
- **Test Results** management
- **Sample Collection** workflow
- **Critical Results** alerts
- **Lab Analytics** and reporting

### 💊 Pharmacy & Inventory
- **Medication Inventory** management
- **Prescription Processing**
- **Stock Level** monitoring
- **Expiry Date** tracking
- **Low Stock** alerts

### 💰 Financial Management
- **Billing System** with itemized charges
- **Payment Processing** and tracking
- **Insurance Claims** management
- **Financial Reports** and analytics
- **Outstanding Payments** tracking

### 👨‍💼 Human Resources
- **Staff Management** and profiles
- **Work Schedules** and shifts
- **Performance Evaluations**
- **Department Management**
- **Staff Analytics**

### 🏢 Facility Management
- **Room Management** and occupancy
- **Equipment Tracking** and maintenance
- **Maintenance Requests** workflow
- **Asset Management**
- **Facility Analytics**

### 📊 Reports & Analytics
- **Real-time Dashboard** with key metrics
- **Comprehensive Reports** across all modules
- **Data Visualization** with charts and graphs
- **Custom Analytics** and insights
- **Export Capabilities**

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **Vite** for build tooling
- **React Router** for navigation

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **bcryptjs** for password hashing
- **Express Validator** for input validation

### Additional Tools
- **Socket.IO** for real-time features
- **Nodemailer** for email notifications
- **Helmet** for security
- **CORS** for cross-origin requests
- **Morgan** for logging

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (local or cloud)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hms_v2
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Seed the database**
   ```bash
   npm run seed
   ```

5. **Start the application**
   ```bash
   npm run dev
   ```

The application will be available at:
- **Frontend**: http://localhost:5173
- **Backend**: http://localhost:3002

### Default Login Credentials
- **Admin**: <EMAIL> / admin123
- **Doctor**: <EMAIL> / doctor123

## 📁 Project Structure

```
hms_v2/
├── src/                          # Frontend source code
│   ├── components/               # React components
│   ├── services/                 # API services
│   ├── types/                    # TypeScript types
│   └── styles/                   # CSS styles
├── server/                       # Backend source code
│   ├── controllers/              # Route controllers
│   ├── models/                   # Database models
│   ├── routes/                   # API routes
│   ├── middleware/               # Custom middleware
│   └── utils/                    # Utility functions
├── public/                       # Static assets
└── docs/                         # Documentation
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/verify` - Verify token
- `POST /api/auth/refresh` - Refresh token

### Patients
- `GET /api/patients` - Get all patients
- `POST /api/patients` - Create patient
- `GET /api/patients/:id` - Get patient by ID
- `PUT /api/patients/:id` - Update patient
- `DELETE /api/patients/:id` - Delete patient

### Appointments
- `GET /api/appointments` - Get all appointments
- `POST /api/appointments` - Create appointment
- `GET /api/appointments/available-slots` - Get available slots
- `GET /api/appointments/doctor/:id/schedule` - Get doctor schedule

### Clinical
- `GET /api/clinical/medical-records` - Get medical records
- `POST /api/clinical/medical-records` - Create medical record
- `GET /api/clinical/patients/:id/history` - Get patient history

### Laboratory
- `GET /api/laboratory/tests` - Get lab tests
- `POST /api/laboratory/tests` - Create lab test
- `GET /api/laboratory/stats` - Get lab statistics

### Pharmacy
- `GET /api/pharmacy/inventory` - Get inventory
- `POST /api/pharmacy/inventory` - Add inventory item
- `GET /api/pharmacy/prescriptions` - Get prescriptions

### Financial
- `GET /api/financial/bills` - Get bills
- `POST /api/financial/bills` - Create bill
- `POST /api/financial/bills/:id/payments` - Add payment

### Reports
- `GET /api/reports/dashboard` - Get dashboard data
- `GET /api/reports/patients` - Get patient analytics
- `GET /api/reports/financial` - Get financial analytics

## 🧪 Testing

### Run API Tests
```bash
node test-all-apis.js
```

### Test Individual Modules
```bash
node test-patients-api.js
node test-appointments-api.js
node test-login.js
```

## 🔒 Security Features

- **JWT Authentication** with secure token handling
- **Password Hashing** using bcryptjs
- **Input Validation** on all endpoints
- **CORS Protection** for cross-origin requests
- **Rate Limiting** to prevent abuse
- **Helmet** for security headers
- **Role-based Access Control** (RBAC)

## 📈 Performance Features

- **Database Indexing** for optimized queries
- **Pagination** for large datasets
- **Caching** strategies
- **Optimized API responses**
- **Lazy Loading** in frontend
- **Code Splitting** for better performance

## 🌐 Deployment

### Production Build
```bash
npm run build
npm start
```

### Environment Variables
```env
MONGODB_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret
NODE_ENV=production
PORT=3002
CLIENT_URL=your_frontend_url
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the API tests for examples

## 🎯 Future Enhancements

- **Mobile App** development
- **Telemedicine** integration
- **AI-powered** diagnostics
- **IoT Device** integration
- **Advanced Analytics** with ML
- **Multi-language** support
- **Cloud Deployment** guides

---

**Built with ❤️ for modern healthcare management**
