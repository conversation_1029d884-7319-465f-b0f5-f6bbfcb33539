#!/bin/bash

# Hospital Management System Deployment Script
echo "🏥 Hospital Management System - Deployment Script"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version check passed: $(node -v)"

# Check if MongoDB is accessible
print_info "Checking MongoDB connection..."
if [ -f ".env" ]; then
    source .env
    if [ -n "$MONGODB_URI" ]; then
        print_status "MongoDB URI found in environment"
    else
        print_warning "MongoDB URI not found in .env file"
    fi
else
    print_warning ".env file not found. Creating from template..."
    cp .env.example .env
    print_info "Please edit .env file with your configuration"
fi

# Install dependencies
print_info "Installing dependencies..."
npm install
if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Build the application
print_info "Building the application..."
npm run build
if [ $? -eq 0 ]; then
    print_status "Application built successfully"
else
    print_error "Failed to build application"
    exit 1
fi

# Seed the database
print_info "Seeding the database..."
npm run seed
if [ $? -eq 0 ]; then
    print_status "Database seeded successfully"
else
    print_warning "Database seeding failed or already seeded"
fi

# Test the APIs
print_info "Testing API endpoints..."
node test-all-apis.js > api_test_results.log 2>&1 &
API_TEST_PID=$!

# Start the server in background for testing
print_info "Starting server for testing..."
npm run server:only > server.log 2>&1 &
SERVER_PID=$!

# Wait a moment for server to start
sleep 5

# Check if server is running
if ps -p $SERVER_PID > /dev/null; then
    print_status "Server started successfully (PID: $SERVER_PID)"
else
    print_error "Failed to start server"
    exit 1
fi

# Wait for API tests to complete
wait $API_TEST_PID

# Stop the test server
kill $SERVER_PID 2>/dev/null

# Check API test results
if grep -q "API Testing Complete" api_test_results.log; then
    print_status "API tests passed successfully"
else
    print_warning "Some API tests may have failed. Check api_test_results.log for details"
fi

# Create production start script
cat > start_production.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Hospital Management System in Production Mode..."

# Set production environment
export NODE_ENV=production

# Start the server
npm start
EOF

chmod +x start_production.sh

# Create systemd service file (optional)
cat > hms.service << 'EOF'
[Unit]
Description=Hospital Management System
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/hms_v2
ExecStart=/usr/bin/npm start
Restart=on-failure
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# Create Docker files for containerization
cat > Dockerfile << 'EOF'
# Multi-stage build for Hospital Management System
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime

WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .

# Build the application
RUN npm run build

EXPOSE 3002

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S hms -u 1001

USER hms

CMD ["npm", "start"]
EOF

cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  hms-app:
    build: .
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/hospital_management
    depends_on:
      - mongo
    restart: unless-stopped

  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./dist:/usr/share/nginx/html
    depends_on:
      - hms-app
    restart: unless-stopped

volumes:
  mongo_data:
EOF

# Create nginx configuration
cat > nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream hms_backend {
        server hms-app:3002;
    }

    server {
        listen 80;
        server_name localhost;

        # Serve static files
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
        }

        # Proxy API requests
        location /api/ {
            proxy_pass http://hms_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF

# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
# Hospital Management System Backup Script

BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="hms_backup_$DATE.tar.gz"

mkdir -p $BACKUP_DIR

echo "Creating backup: $BACKUP_FILE"

# Backup database
mongodump --uri="$MONGODB_URI" --out="$BACKUP_DIR/db_$DATE"

# Backup application files
tar -czf "$BACKUP_DIR/$BACKUP_FILE" \
    --exclude=node_modules \
    --exclude=dist \
    --exclude=.git \
    --exclude=backups \
    .

echo "Backup completed: $BACKUP_DIR/$BACKUP_FILE"
EOF

chmod +x backup.sh

print_status "Deployment preparation completed!"
echo ""
print_info "📋 Next Steps:"
echo "1. Edit .env file with your production configuration"
echo "2. Run './start_production.sh' to start in production mode"
echo "3. For Docker deployment: 'docker-compose up -d'"
echo "4. For systemd service: Copy hms.service to /etc/systemd/system/"
echo "5. Create regular backups with './backup.sh'"
echo ""
print_info "🔗 Access URLs:"
echo "- Frontend: http://localhost:5173 (development)"
echo "- Backend API: http://localhost:3002/api"
echo "- Health Check: http://localhost:3002/health"
echo ""
print_info "🔐 Default Login:"
echo "- Admin: <EMAIL> / admin123"
echo "- Doctor: <EMAIL> / doctor123"
echo ""
print_status "Hospital Management System is ready for deployment! 🎉"
