import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/hospital_management';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ Database connection error:', error);
    process.exit(1);
  }
};

const PatientSchema = new mongoose.Schema({
  patientId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'PT' + Date.now().toString().slice(-6);
    }
  },
  firstName: { type: String, required: true, trim: true },
  lastName: { type: String, required: true, trim: true },
  dateOfBirth: { type: Date, required: true },
  gender: { type: String, required: true, enum: ['Male', 'Female', 'Other'] },
  email: { type: String, lowercase: true, trim: true, sparse: true },
  phone: { type: String, required: true, trim: true },
  address: {
    street: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    zipCode: { type: String, required: true },
    country: { type: String, required: true, default: 'USA' }
  },
  emergencyContact: {
    name: { type: String, required: true },
    relationship: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String }
  },
  insurance: {
    provider: String,
    policyNumber: String,
    groupNumber: String,
    expiryDate: Date
  },
  medicalHistory: {
    allergies: [String],
    chronicConditions: [String],
    medications: [String],
    surgeries: [{
      procedure: String,
      date: Date,
      hospital: String
    }]
  },
  bloodType: { type: String, enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'] },
  status: { type: String, enum: ['Active', 'Inactive', 'Deceased'], default: 'Active' },
  admissionDate: Date,
  dischargeDate: Date,
  assignedDoctor: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  room: String,
  notes: String,
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const Patient = mongoose.model('Patient', PatientSchema);

const createSamplePatients = async () => {
  await connectDB();
  
  try {
    // Clear existing patients
    await Patient.deleteMany({});
    
    const samplePatients = [
      {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: new Date('1985-06-15'),
        gender: 'Male',
        email: '<EMAIL>',
        phone: '******-0101',
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        emergencyContact: {
          name: 'Jane Doe',
          relationship: 'Spouse',
          phone: '******-0102',
          email: '<EMAIL>'
        },
        insurance: {
          provider: 'Blue Cross Blue Shield',
          policyNumber: 'BC123456789',
          groupNumber: 'GRP001',
          expiryDate: new Date('2025-12-31')
        },
        medicalHistory: {
          allergies: ['Penicillin', 'Shellfish'],
          chronicConditions: ['Hypertension'],
          medications: ['Lisinopril 10mg'],
          surgeries: []
        },
        bloodType: 'O+',
        status: 'Active'
      },
      {
        firstName: 'Sarah',
        lastName: 'Johnson',
        dateOfBirth: new Date('1992-03-22'),
        gender: 'Female',
        email: '<EMAIL>',
        phone: '******-0201',
        address: {
          street: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90210',
          country: 'USA'
        },
        emergencyContact: {
          name: 'Mike Johnson',
          relationship: 'Brother',
          phone: '******-0202',
          email: '<EMAIL>'
        },
        insurance: {
          provider: 'Aetna',
          policyNumber: 'AET987654321',
          groupNumber: 'GRP002',
          expiryDate: new Date('2025-12-31')
        },
        medicalHistory: {
          allergies: ['Latex'],
          chronicConditions: [],
          medications: [],
          surgeries: [{
            procedure: 'Appendectomy',
            date: new Date('2020-05-15'),
            hospital: 'General Hospital'
          }]
        },
        bloodType: 'A+',
        status: 'Active'
      },
      {
        firstName: 'Michael',
        lastName: 'Brown',
        dateOfBirth: new Date('1978-11-08'),
        gender: 'Male',
        email: '<EMAIL>',
        phone: '******-0301',
        address: {
          street: '789 Pine St',
          city: 'Chicago',
          state: 'IL',
          zipCode: '60601',
          country: 'USA'
        },
        emergencyContact: {
          name: 'Lisa Brown',
          relationship: 'Wife',
          phone: '******-0302',
          email: '<EMAIL>'
        },
        insurance: {
          provider: 'Cigna',
          policyNumber: 'CIG456789123',
          groupNumber: 'GRP003',
          expiryDate: new Date('2025-12-31')
        },
        medicalHistory: {
          allergies: [],
          chronicConditions: ['Diabetes Type 2', 'High Cholesterol'],
          medications: ['Metformin 500mg', 'Atorvastatin 20mg'],
          surgeries: []
        },
        bloodType: 'B+',
        status: 'Active'
      },
      {
        firstName: 'Emily',
        lastName: 'Davis',
        dateOfBirth: new Date('1995-07-30'),
        gender: 'Female',
        email: '<EMAIL>',
        phone: '******-0401',
        address: {
          street: '321 Elm St',
          city: 'Houston',
          state: 'TX',
          zipCode: '77001',
          country: 'USA'
        },
        emergencyContact: {
          name: 'Robert Davis',
          relationship: 'Father',
          phone: '******-0402',
          email: '<EMAIL>'
        },
        insurance: {
          provider: 'UnitedHealthcare',
          policyNumber: 'UHC789123456',
          groupNumber: 'GRP004',
          expiryDate: new Date('2025-12-31')
        },
        medicalHistory: {
          allergies: ['Peanuts'],
          chronicConditions: ['Asthma'],
          medications: ['Albuterol Inhaler'],
          surgeries: []
        },
        bloodType: 'AB-',
        status: 'Active'
      },
      {
        firstName: 'David',
        lastName: 'Wilson',
        dateOfBirth: new Date('1960-12-12'),
        gender: 'Male',
        email: '<EMAIL>',
        phone: '******-0501',
        address: {
          street: '654 Maple Ave',
          city: 'Phoenix',
          state: 'AZ',
          zipCode: '85001',
          country: 'USA'
        },
        emergencyContact: {
          name: 'Mary Wilson',
          relationship: 'Wife',
          phone: '******-0502',
          email: '<EMAIL>'
        },
        insurance: {
          provider: 'Medicare',
          policyNumber: 'MED123456789',
          groupNumber: 'MED001',
          expiryDate: new Date('2025-12-31')
        },
        medicalHistory: {
          allergies: ['Sulfa drugs'],
          chronicConditions: ['Arthritis', 'Heart Disease'],
          medications: ['Aspirin 81mg', 'Metoprolol 50mg'],
          surgeries: [{
            procedure: 'Knee Replacement',
            date: new Date('2022-08-20'),
            hospital: 'Phoenix Medical Center'
          }]
        },
        bloodType: 'O-',
        status: 'Active'
      }
    ];

    const createdPatients = await Patient.insertMany(samplePatients);
    console.log(`✅ Created ${createdPatients.length} sample patients`);
    
    // Display created patients
    createdPatients.forEach(patient => {
      console.log(`- ${patient.patientId}: ${patient.firstName} ${patient.lastName}`);
    });
    
  } catch (error) {
    console.error('Error creating sample patients:', error);
  }
  
  process.exit(0);
};

createSamplePatients();
