// Test script for patients API
const testPatientsAPI = async () => {
  try {
    // First login to get token
    const loginResponse = await fetch('http://localhost:3002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    
    if (!loginData.success) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginData.token;
    console.log('✅ Login successful');

    // Test getting all patients
    console.log('\n📋 Testing GET /api/patients');
    const patientsResponse = await fetch('http://localhost:3002/api/patients', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const patientsData = await patientsResponse.json();
    console.log('Patients Response:', JSON.stringify(patientsData, null, 2));

    if (patientsData.success && patientsData.data.length > 0) {
      // Test getting a single patient
      const patientId = patientsData.data[0]._id;
      console.log(`\n👤 Testing GET /api/patients/${patientId}`);
      
      const patientResponse = await fetch(`http://localhost:3002/api/patients/${patientId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const patientData = await patientResponse.json();
      console.log('Single Patient Response:', JSON.stringify(patientData, null, 2));
    }

    // Test creating a new patient
    console.log('\n➕ Testing POST /api/patients');
    const newPatient = {
      firstName: 'Test',
      lastName: 'Patient',
      dateOfBirth: '1990-01-01',
      gender: 'Male',
      email: '<EMAIL>',
      phone: '******-9999',
      address: {
        street: '999 Test St',
        city: 'Test City',
        state: 'TC',
        zipCode: '99999',
        country: 'USA'
      },
      emergencyContact: {
        name: 'Test Contact',
        relationship: 'Friend',
        phone: '******-9998'
      },
      bloodType: 'A+',
      status: 'Active'
    };

    const createResponse = await fetch('http://localhost:3002/api/patients', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(newPatient)
    });

    const createData = await createResponse.json();
    console.log('Create Patient Response:', JSON.stringify(createData, null, 2));

  } catch (error) {
    console.error('Error:', error);
  }
};

testPatientsAPI();
