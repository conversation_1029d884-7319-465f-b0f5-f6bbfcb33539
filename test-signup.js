// Test signup functionality
const BASE_URL = 'http://localhost:3002/api';

const testSignup = async () => {
  try {
    console.log('🧪 Testing User Registration...');

    const newUser = {
      username: 'testuser123',
      email: '<EMAIL>',
      password: 'testpass123',
      firstName: 'Test',
      lastName: 'User',
      department: 'General'
    };

    const response = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newUser),
    });

    const data = await response.json();

    if (response.ok && data.success) {
      console.log('✅ User registration successful!');
      console.log(`   - User ID: ${data.user._id}`);
      console.log(`   - Username: ${data.user.username}`);
      console.log(`   - Email: ${data.user.email}`);
      console.log(`   - Name: ${data.user.firstName} ${data.user.lastName}`);
      console.log(`   - Department: ${data.user.department}`);
      console.log(`   - Role: ${data.user.role?.name || 'Default'}`);
      console.log(`   - Token received: ${data.token ? 'Yes' : 'No'}`);

      // Test login with new user
      console.log('\n🔐 Testing login with new user...');
      const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: newUser.email,
          password: newUser.password
        }),
      });

      const loginData = await loginResponse.json();

      if (loginResponse.ok && loginData.success) {
        console.log('✅ Login with new user successful!');
        console.log(`   - Welcome back: ${loginData.user.firstName} ${loginData.user.lastName}`);
      } else {
        console.log('❌ Login with new user failed:', loginData.error);
      }

    } else {
      console.log('❌ User registration failed:', data.error);
    }

  } catch (error) {
    console.error('❌ Error during signup testing:', error.message);
  }
};

testSignup();
