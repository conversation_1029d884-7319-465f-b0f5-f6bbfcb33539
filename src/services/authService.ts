import { User } from '../types/auth';

const API_BASE_URL = import.meta.env.VITE_REACT_APP_API_URL || 'http://localhost:3002/api';

class AuthService {
  async login(email: string, password: string): Promise<{ user: User; token: string; refreshToken: string }> {
    console.log('AuthService: Making login request to', `${API_BASE_URL}/auth/login`);

    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      console.log('AuthService: Response status:', response.status);

      if (!response.ok) {
        const error = await response.json();
        console.error('AuthService: Login failed with error:', error);

        // Create a more specific error message based on error type
        let errorMessage = error.error || 'Login failed';

        switch (error.errorType) {
          case 'MISSING_CREDENTIALS':
            errorMessage = 'Please provide both email and password';
            break;
          case 'INVALID_EMAIL_FORMAT':
            errorMessage = 'Please provide a valid email address';
            break;
          case 'USER_NOT_FOUND':
            errorMessage = 'No account found with this email address. Please check your email or sign up for a new account.';
            break;
          case 'ACCOUNT_DEACTIVATED':
            errorMessage = 'Your account has been deactivated. Please contact the administrator for assistance.';
            break;
          case 'INVALID_PASSWORD':
            errorMessage = 'Incorrect password. Please check your password and try again.';
            break;
          case 'INVALID_ROLE':
            errorMessage = 'Your account role is not properly configured. Please contact the administrator.';
            break;
          case 'DATABASE_ERROR':
            errorMessage = 'Database connection error. Please try again later.';
            break;
          case 'SERVER_ERROR':
            errorMessage = 'An unexpected error occurred. Please try again later.';
            break;
          default:
            errorMessage = error.error || 'Login failed. Please check your credentials and try again.';
        }

        const customError = new Error(errorMessage);
        (customError as any).errorType = error.errorType;
        throw customError;
      }

      const data = await response.json();
      console.log('AuthService: Login successful, received data:', { ...data, token: '***' });
      return data;
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Unable to connect to the server. Please check your internet connection and try again.');
      }
      throw error;
    }
  }

  async register(userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    department?: string;
  }): Promise<{ user: User; token: string; refreshToken: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const error = await response.json();

        // Create a more specific error message based on error type
        let errorMessage = error.error || 'Registration failed';

        switch (error.errorType) {
          case 'MISSING_REQUIRED_FIELDS':
            errorMessage = 'Please fill in all required fields';
            break;
          case 'INVALID_EMAIL_FORMAT':
            errorMessage = 'Please provide a valid email address';
            break;
          case 'WEAK_PASSWORD':
            errorMessage = 'Password must be at least 6 characters long';
            break;
          case 'USER_ALREADY_EXISTS':
            errorMessage = `An account with this ${error.conflictField} already exists. Please use a different ${error.conflictField} or try logging in.`;
            break;
          case 'DUPLICATE_FIELD':
            errorMessage = `An account with this ${error.field} already exists`;
            break;
          case 'VALIDATION_ERROR':
            errorMessage = error.error || 'Please check your input and try again';
            break;
          case 'MISSING_DEFAULT_ROLE':
            errorMessage = 'System configuration error. Please contact the administrator.';
            break;
          case 'SERVER_ERROR':
            errorMessage = 'An unexpected error occurred during registration. Please try again later.';
            break;
          default:
            errorMessage = error.error || 'Registration failed. Please try again.';
        }

        const customError = new Error(errorMessage);
        (customError as any).errorType = error.errorType;
        throw customError;
      }

      return response.json();
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Unable to connect to the server. Please check your internet connection and try again.');
      }
      throw error;
    }
  }

  async verifyToken(token: string): Promise<User> {
    const response = await fetch(`${API_BASE_URL}/auth/verify`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Token verification failed');
    }

    const data = await response.json();
    return data.user;
  }

  async updateUserPermissions(userId: string, permissions: string[]): Promise<User> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/users/${userId}/permissions`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ permissions }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update permissions');
    }

    return response.json();
  }

  async getAllUsers(): Promise<User[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/users`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch users');
    }

    return response.json();
  }

  async getAllRoles() {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/roles`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch roles');
    }

    return response.json();
  }

  async getAllPermissions() {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/permissions`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch permissions');
    }

    return response.json();
  }

  async refreshToken(): Promise<{ user: User; token: string }> {
    const refreshToken = localStorage.getItem('refreshToken');

    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        const error = await response.json();

        // Handle specific refresh token errors
        let errorMessage = error.error || 'Token refresh failed';

        switch (error.errorType) {
          case 'MISSING_REFRESH_TOKEN':
            errorMessage = 'Session expired. Please log in again.';
            break;
          case 'INVALID_REFRESH_TOKEN':
            errorMessage = 'Invalid session. Please log in again.';
            break;
          case 'REFRESH_TOKEN_EXPIRED':
            errorMessage = 'Session expired. Please log in again.';
            break;
          case 'ACCOUNT_DEACTIVATED':
            errorMessage = 'Your account has been deactivated. Please contact the administrator.';
            break;
          case 'SERVER_ERROR':
            errorMessage = 'Server error. Please try again later.';
            break;
        }

        // Clear tokens on refresh failure
        this.logout();

        const customError = new Error(errorMessage);
        (customError as any).errorType = error.errorType;
        throw customError;
      }

      const data = await response.json();

      // Update stored token
      localStorage.setItem('token', data.token);

      return data;
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Unable to connect to the server. Please check your internet connection.');
      }
      throw error;
    }
  }

  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
  }
}

export const authService = new AuthService();