import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users,
  Bed,
  Calendar,
  DollarSign,
  TrendingUp,
  Activity,
  AlertCircle,
  Clock,
  RefreshCw,
  Loader,
  UserPlus,
  TestTube,
  Heart,
  Shield,
  Zap
} from 'lucide-react';
import { StatsCard } from './StatsCard';
import { RecentPatients } from './RecentPatients';
import { AppointmentSchedule } from './AppointmentSchedule';
import { QuickActions } from './QuickActions';
import { reportsAPI } from '../services/apiService';

interface ActivityItem {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  status: string;
  icon: string;
  color: string;
}

export function Dashboard() {
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([]);
  const [patientAnalytics, setPatientAnalytics] = useState<any>(null);
  const [appointmentAnalytics, setAppointmentAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activityLoading, setActivityLoading] = useState(true);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const handleNavigate = (module: string) => {
    navigate(`/${module}`);
  };

  useEffect(() => {
    fetchDashboardData();
    fetchRecentActivity();
    fetchAnalytics();

    // Set up auto-refresh every 5 minutes
    const interval = setInterval(() => {
      fetchDashboardData(true);
      fetchRecentActivity(true);
      fetchAnalytics(true);
      setLastUpdated(new Date());
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const data = await reportsAPI.getDashboard();
      setDashboardData(data.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
      // Set fallback data
      setDashboardData({
        patients: { total: 0, newThisMonth: 0 },
        appointments: { today: 0, scheduled: 0 },
        financial: { monthlyRevenue: 0, outstandingAmount: 0 },
        laboratory: { pendingTests: 0 },
        inventory: { lowStockItems: 0 }
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchRecentActivity = async (isRefresh = false) => {
    try {
      if (!isRefresh) {
        setActivityLoading(true);
      }

      const response = await reportsAPI.getRecentActivity({ limit: 8, hours: 24 });
      if (response.success) {
        setRecentActivity(response.data);
      }
    } catch (err) {
      console.error('Error fetching recent activity:', err);
      // Set fallback activity data
      setRecentActivity([]);
    } finally {
      setActivityLoading(false);
    }
  };

  const fetchAnalytics = async (isRefresh = false) => {
    try {
      if (!isRefresh) {
        setAnalyticsLoading(true);
      }

      const [patientData, appointmentData] = await Promise.all([
        reportsAPI.getPatientAnalytics('7'),
        reportsAPI.getAppointmentAnalytics('7')
      ]);

      if (patientData.success) {
        setPatientAnalytics(patientData.data);
      }
      if (appointmentData.success) {
        setAppointmentAnalytics(appointmentData.data);
      }
    } catch (err) {
      console.error('Error fetching analytics:', err);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchDashboardData(true);
    fetchRecentActivity(true);
    fetchAnalytics(true);
    setLastUpdated(new Date());
  };

  const getActivityIcon = (iconName: string) => {
    switch (iconName) {
      case 'user-plus': return UserPlus;
      case 'calendar': return Calendar;
      case 'test-tube': return TestTube;
      case 'dollar-sign': return DollarSign;
      default: return Activity;
    }
  };

  const getTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} min ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <div className="text-sm text-gray-500">Loading...</div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Last updated: {lastUpdated.toLocaleString()}
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-2 px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 disabled:opacity-50"
            title="Refresh Dashboard"
          >
            <RefreshCw size={14} className={refreshing ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Patients"
          value={dashboardData?.patients?.total?.toLocaleString() || '0'}
          change={`+${dashboardData?.patients?.newThisMonth || 0} this month`}
          changeType="increase"
          icon={Users}
          color="blue"
        />
        <StatsCard
          title="Today's Appointments"
          value={dashboardData?.appointments?.today?.toString() || '0'}
          change={`${dashboardData?.appointments?.scheduled || 0} scheduled`}
          changeType={dashboardData?.appointments?.today > 0 ? "increase" : "neutral"}
          icon={Calendar}
          color="purple"
        />
        <StatsCard
          title="Monthly Revenue"
          value={`$${(dashboardData?.financial?.monthlyRevenue || 0).toLocaleString()}`}
          change={`$${(dashboardData?.financial?.outstandingAmount || 0).toLocaleString()} outstanding`}
          changeType={dashboardData?.financial?.monthlyRevenue > 0 ? "increase" : "neutral"}
          icon={DollarSign}
          color="emerald"
        />
        <StatsCard
          title="Pending Lab Tests"
          value={dashboardData?.laboratory?.pendingTests?.toString() || '0'}
          change={`${dashboardData?.inventory?.lowStockItems || 0} low stock items`}
          changeType={dashboardData?.laboratory?.pendingTests > 5 ? "decrease" : "neutral"}
          icon={Activity}
          color="orange"
        />
      </div>

      {/* Additional Stats Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <Heart className="h-5 w-5 text-red-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Critical Patients</p>
              <p className="text-lg font-semibold text-gray-900">
                {patientAnalytics?.statusDistribution?.find((s: any) => s._id === 'Critical')?.count || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Shield className="h-5 w-5 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Stable Patients</p>
              <p className="text-lg font-semibold text-gray-900">
                {patientAnalytics?.statusDistribution?.find((s: any) => s._id === 'Stable')?.count || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Bed className="h-5 w-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Bed Occupancy</p>
              <p className="text-lg font-semibold text-gray-900">
                {Math.round(((dashboardData?.patients?.total || 0) / 100) * 100)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <TestTube className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Lab Tests</p>
              <p className="text-lg font-semibold text-gray-900">
                {dashboardData?.laboratory?.pendingTests || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Zap className="h-5 w-5 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">System Status</p>
              <p className="text-lg font-semibold text-green-600">Online</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - 2/3 width */}
        <div className="lg:col-span-2 space-y-6">
          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className="p-1 text-gray-500 hover:text-gray-700 disabled:opacity-50"
                  title="Refresh"
                >
                  <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
                </button>
                <Activity size={20} className="text-gray-500" />
              </div>
            </div>

            {activityLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader className="animate-spin mr-2" size={20} />
                <span className="text-gray-500">Loading recent activity...</span>
              </div>
            ) : recentActivity.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="mx-auto mb-2 text-gray-400" size={24} />
                <p className="text-gray-500 text-sm">No recent activity found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentActivity.map((activity) => {
                  const IconComponent = getActivityIcon(activity.icon);
                  const colorClasses = {
                    green: 'bg-green-500',
                    blue: 'bg-blue-500',
                    purple: 'bg-purple-500',
                    yellow: 'bg-yellow-500',
                    red: 'bg-red-500'
                  };

                  return (
                    <div key={activity.id} className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                      <div className={`w-2 h-2 rounded-full ${colorClasses[activity.color as keyof typeof colorClasses] || 'bg-gray-500'}`}></div>
                      <div className="flex-shrink-0">
                        <IconComponent size={16} className={`text-${activity.color}-600`} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{activity.title}</p>
                        <p className="text-xs text-gray-500 truncate">{activity.description}</p>
                      </div>
                      <span className="text-xs text-gray-500 flex-shrink-0">
                        {getTimeAgo(activity.timestamp)}
                      </span>
                    </div>
                  );
                })}
              </div>
            )}

            <div className="mt-4 text-center">
              <button
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                onClick={() => navigate('/reports')}
              >
                View All Activity
              </button>
            </div>
          </div>

          {/* Recent Patients */}
          <RecentPatients />
        </div>

        {/* Right Column - 1/3 width */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <QuickActions onNavigate={handleNavigate} />

          {/* Today's Schedule */}
          <AppointmentSchedule />

          {/* Alerts */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Alerts</h2>
              <AlertCircle size={20} className="text-orange-500" />
            </div>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-red-900">Critical Patient</p>
                  <p className="text-xs text-red-700">Room 205 - Requires immediate attention</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-yellow-900">Low Inventory</p>
                  <p className="text-xs text-yellow-700">Paracetamol - 5 units remaining</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}