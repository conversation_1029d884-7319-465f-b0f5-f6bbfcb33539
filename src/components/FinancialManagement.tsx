import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  CreditCard,
  Receipt,
  TrendingUp,
  TrendingDown,
  Calendar,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Loader,
  X,
  BarChart3,
  User,
  Building,
  PieChart,
  Activity,
  Target,
  Zap,
  Bell,
  RefreshCw,
  Download,
  Printer
} from 'lucide-react';
import { financialAPI, patientAPI, appointmentAPI, reportsAPI } from '../services/apiService';

interface Bill {
  _id: string;
  billId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
  billDate: string;
  dueDate: string;
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    category: string;
  }>;
  subtotal: number;
  tax: number;
  discount: number;
  totalAmount: number;
  paidAmount: number;
  balanceAmount: number;
  paymentStatus: string;
  paymentMethod?: string;
  insurance?: {
    provider: string;
    policyNumber: string;
    claimAmount: number;
    claimStatus: string;
  };
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export function FinancialManagement() {
  const [bills, setBills] = useState<Bill[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Real-time analytics states
  const [financialStats, setFinancialStats] = useState<any>(null);
  const [revenueAnalytics, setRevenueAnalytics] = useState<any>(null);
  const [paymentTrends, setPaymentTrends] = useState<any[]>([]);
  const [overdueAlerts, setOverdueAlerts] = useState<Bill[]>([]);
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Modal states
  const [showCreateBillModal, setShowCreateBillModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showBillDetailsModal, setShowBillDetailsModal] = useState(false);
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false);

  // Form data
  const [billFormData, setBillFormData] = useState({
    patient: '',
    appointment: '',
    items: [{ description: '', quantity: 1, unitPrice: 0, category: 'Consultation' }],
    tax: 0,
    discount: 0,
    notes: '',
    dueDate: '',
    insurance: {
      provider: '',
      policyNumber: '',
      claimAmount: 0
    }
  });

  const [paymentFormData, setPaymentFormData] = useState({
    amount: 0,
    method: 'Cash',
    reference: ''
  });

  // Additional data
  const [patients, setPatients] = useState<any[]>([]);
  const [appointments, setAppointments] = useState<any[]>([]);
//   const [financialStats, setFinancialStats] = useState<any>(null);
  const [formLoading, setFormLoading] = useState(false);

  // Fetch bills from API
  useEffect(() => {
    fetchBills();
    fetchPatients();
    fetchAppointments();
    fetchFinancialStats();
    fetchRevenueAnalytics();
    fetchPaymentTrends();
    checkOverdueAlerts();
  }, [currentPage, searchTerm, statusFilter]);

  // Real-time monitoring effect
  useEffect(() => {
    if (realTimeUpdates) {
      const interval = setInterval(() => {
        fetchFinancialStats();
        fetchRevenueAnalytics();
        checkOverdueAlerts();
      }, 60000); // Update every minute

      return () => clearInterval(interval);
    }
  }, [realTimeUpdates]);

  // Real-time analytics functions
  const fetchRevenueAnalytics = async () => {
    try {
      const response = await reportsAPI.getFinancialAnalytics('30');
      if (response.success) {
        setRevenueAnalytics(response.data);
      }
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
    }
  };

  const fetchPaymentTrends = async () => {
    try {
      const response = await financialAPI.getPaymentTrends();
      if (response.success) {
        setPaymentTrends(response.data);
      }
    } catch (error) {
      console.error('Error fetching payment trends:', error);
    }
  };

  const checkOverdueAlerts = async () => {
    try {
      const response = await financialAPI.getOverdueBills();
      if (response.success) {
        setOverdueAlerts(response.data);
      }
    } catch (error) {
      console.error('Error checking overdue alerts:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchBills(),
        fetchFinancialStats(),
        fetchRevenueAnalytics(),
        fetchPaymentTrends(),
        checkOverdueAlerts()
      ]);
    } finally {
      setRefreshing(false);
    }
  };

  const fetchBills = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        limit: 10
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (statusFilter !== 'all') {
        params.paymentStatus = statusFilter;
      }

      const response = await financialAPI.getBills(params);

      if (response.success) {
        setBills(response.data);
        setTotalPages(response.pagination?.pages || 1);
        setError(null);
      } else {
        setError('Failed to fetch bills');
      }
    } catch (err) {
      console.error('Error fetching bills:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch bills');
    } finally {
      setLoading(false);
    }
  };

  const fetchFinancialStats = async () => {
    try {
      const response = await financialAPI.getStats();
      if (response.success) {
        setFinancialStats(response.data);
      }
    } catch (err) {
      console.error('Error fetching financial stats:', err);
    }
  };

  const fetchPatients = async () => {
    try {
      const response = await patientAPI.getAll({ limit: 100 });
      if (response.success) {
        setPatients(response.data);
      }
    } catch (err) {
      console.error('Error fetching patients:', err);
    }
  };

  const fetchAppointments = async () => {
    try {
      const response = await appointmentAPI.getAll({ limit: 100 });
      if (response.success) {
        setAppointments(response.data);
      }
    } catch (err) {
      console.error('Error fetching appointments:', err);
    }
  };

  const calculateItemTotal = (quantity: number, unitPrice: number) => {
    return quantity * unitPrice;
  };

  const calculateBillTotal = () => {
    const subtotal = billFormData.items.reduce((sum, item) =>
      sum + calculateItemTotal(item.quantity, item.unitPrice), 0
    );
    return subtotal + billFormData.tax - billFormData.discount;
  };

  const handleBillFormChange = (field: string, value: any) => {
    setBillFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setBillFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const addBillItem = () => {
    setBillFormData(prev => ({
      ...prev,
      items: [...prev.items, { description: '', quantity: 1, unitPrice: 0, category: 'Consultation' }]
    }));
  };

  const removeBillItem = (index: number) => {
    setBillFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const resetBillForm = () => {
    setBillFormData({
      patient: '',
      appointment: '',
      items: [{ description: '', quantity: 1, unitPrice: 0, category: 'Consultation' }],
      tax: 0,
      discount: 0,
      notes: '',
      dueDate: '',
      insurance: {
        provider: '',
        policyNumber: '',
        claimAmount: 0
      }
    });
  };

  const resetPaymentForm = () => {
    setPaymentFormData({
      amount: 0,
      method: 'Cash',
      reference: ''
    });
  };

  const handleCreateBill = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setFormLoading(true);

      const billData = {
        ...billFormData,
        items: billFormData.items.map(item => ({
          ...item,
          totalPrice: calculateItemTotal(item.quantity, item.unitPrice)
        })),
        dueDate: new Date(billFormData.dueDate).toISOString()
      };

      const response = await financialAPI.createBill(billData);

      if (response.success) {
        await fetchBills();
        await fetchFinancialStats();
        setShowCreateBillModal(false);
        resetBillForm();
      } else {
        setError(response.error || 'Failed to create bill');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create bill');
    } finally {
      setFormLoading(false);
    }
  };

  const handleAddPayment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedBill) return;

    try {
      setFormLoading(true);

      const response = await financialAPI.addPayment(selectedBill._id, paymentFormData);

      if (response.success) {
        await fetchBills();
        await fetchFinancialStats();
        setShowPaymentModal(false);
        resetPaymentForm();
        setSelectedBill(null);
      } else {
        setError(response.error || 'Failed to add payment');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add payment');
    } finally {
      setFormLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid': return 'bg-green-100 text-green-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Overdue': return 'bg-red-100 text-red-800';
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };


  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);
  const endDate = new Date();

  const recentBills = bills.filter(bill => bill.billDate >= startDate && bill.billDate <= endDate);

  const totalRevenue = recentBills.reduce((sum, bill) => sum + bill.amount, 0);
  const pendingAmount = recentBills.filter(bill => bill.status === 'Pending').reduce((sum, bill) => sum + bill.amount, 0);
  const overdueAmount = recentBills.filter(bill => bill.status === 'Overdue').reduce((sum, bill) => sum + bill.amount, 0);
  const paidAmount = recentBills.filter(bill => bill.status === 'Paid').reduce((sum, bill) => sum + bill.amount, 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Financial Management</h1>
          {/* Real-time Status Indicators */}
          <div className="flex items-center space-x-4 mt-2">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${realTimeUpdates ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
              <span className="text-sm text-gray-600">
                {realTimeUpdates ? 'Live Updates' : 'Updates Paused'}
              </span>
            </div>
            {overdueAlerts.length > 0 && (
              <div className="flex items-center space-x-2 text-red-600">
                <AlertCircle size={16} />
                <span className="text-sm font-medium">{overdueAlerts.length} Overdue Bill{overdueAlerts.length > 1 ? 's' : ''}</span>
              </div>
            )}
            {financialStats && (
              <div className="flex items-center space-x-2 text-green-600">
                <TrendingUp size={16} />
                <span className="text-sm font-medium">
                  ${financialStats.monthlyRevenue?.toLocaleString() || '0'} This Month
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <RefreshCw size={20} className={refreshing ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          {overdueAlerts.length > 0 && (
            <button
              onClick={() => setShowAnalyticsModal(true)}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              <Bell size={20} />
              <span>Alerts ({overdueAlerts.length})</span>
            </button>
          )}
          <button
            onClick={() => setRealTimeUpdates(!realTimeUpdates)}
            className={`${realTimeUpdates ? 'bg-green-500 hover:bg-green-600' : 'bg-gray-500 hover:bg-gray-600'} text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors`}
          >
            <Zap size={20} />
            <span>{realTimeUpdates ? 'Live' : 'Manual'}</span>
          </button>
          <button
            onClick={() => setShowCreateBillModal(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Receipt size={20} />
            <span>Create Invoice</span>
          </button>
          <button
            onClick={() => setShowAnalyticsModal(true)}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <BarChart3 size={20} />
            <span>Analytics</span>
          </button>
        </div>
      </div>

      {/* Financial Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                ${financialStats?.totalRevenue?.toLocaleString() || '0'}
              </p>
              <p className="text-xs text-green-600 flex items-center mt-1">
                <TrendingUp size={12} className="mr-1" />
                This month
              </p>
            </div>
            <DollarSign size={24} className="text-green-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Outstanding Amount</p>
              <p className="text-2xl font-bold text-gray-900">
                ${financialStats?.outstandingAmount?.toLocaleString() || '0'}
              </p>
              <p className="text-xs text-yellow-600 flex items-center mt-1">
                <Clock size={12} className="mr-1" />
                Pending payments
              </p>
            </div>
            <Clock size={24} className="text-yellow-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Overdue Bills</p>
              <p className="text-2xl font-bold text-gray-900">
                {financialStats?.overdueBills || '0'}
              </p>
              <p className="text-xs text-red-600 flex items-center mt-1">
                <AlertCircle size={12} className="mr-1" />
                Require attention
              </p>
            </div>
            <AlertCircle size={24} className="text-red-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Paid Bills</p>
              <p className="text-2xl font-bold text-gray-900">
                {financialStats?.paidBills || '0'}
              </p>
              <p className="text-xs text-green-600 flex items-center mt-1">
                <CheckCircle size={12} className="mr-1" />
                This month
              </p>
            </div>
            <CheckCircle size={24} className="text-green-500" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('billing')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'billing'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Billing & Invoices
            </button>
            <button
              onClick={() => setActiveTab('insurance')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'insurance'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Insurance Claims
            </button>
            <button
              onClick={() => setActiveTab('reports')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'reports'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Financial Reports
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Revenue Chart Placeholder */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Revenue Trend</h3>
                  <div className="h-64 flex items-center justify-center">
                    <div className="text-center">
                      <TrendingUp size={48} className="text-gray-300 mx-auto mb-2" />
                      <p className="text-gray-500">Revenue chart visualization</p>
                    </div>
                  </div>
                </div>

                {/* Payment Methods */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Methods</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CreditCard size={16} className="text-blue-500" />
                        <span className="text-sm text-gray-700">Credit Card</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">45%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Receipt size={16} className="text-green-500" />
                        <span className="text-sm text-gray-700">Cash</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">30%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <FileText size={16} className="text-purple-500" />
                        <span className="text-sm text-gray-700">Insurance</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">25%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'billing' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Recent Invoices</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Calendar size={16} />
                  <span>Last 30 days</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Invoice ID</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Patient</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Amount</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Services</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Due Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {recentBills.map((bill) => (
                      <tr key={bill.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{bill.id}</td>
                        <td className="py-4 px-6">
                          <div className="font-medium text-gray-900">{bill.patientName}</div>
                          <div className="text-sm text-gray-500">{bill.patientId}</div>
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">{bill.date}</td>
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">
                          ${bill.amount.toLocaleString()}
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">
                          {bill.services.join(', ')}
                        </td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(bill.status)}`}>
                            {bill.status}
                          </span>
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">{bill.dueDate}</td>
                        <td className="py-4 px-6">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-800 text-sm">View</button>
                            <button className="text-green-600 hover:text-green-800 text-sm">Payment</button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'insurance' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Insurance Claims</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <FileText size={16} />
                  <span>Claims management</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Claim ID</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Patient</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Insurance Provider</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Claim Amount</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Approved Amount</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Submission Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {insuranceClaims.map((claim) => (
                      <tr key={claim.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{claim.id}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{claim.patientName}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{claim.insuranceProvider}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">
                          ${claim.claimAmount.toLocaleString()}
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">
                          {claim.approvedAmount > 0 ? `$${claim.approvedAmount.toLocaleString()}` : '-'}
                        </td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(claim.status)}`}>
                            {claim.status}
                          </span>
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">{claim.submissionDate}</td>
                        <td className="py-4 px-6">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-800 text-sm">View</button>
                            <button className="text-green-600 hover:text-green-800 text-sm">Resubmit</button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'reports' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Financial Reports</h2>
              
              <div className="text-center py-12">
                <BarChart3 size={64} className="text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Financial Analytics</h3>
                <p className="text-gray-500">Generate detailed financial reports and analytics.</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Bill Modal */}
      {showCreateBillModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Create New Invoice</h2>
                <button
                  onClick={() => {
                    setShowCreateBillModal(false);
                    resetBillForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>

              <form onSubmit={handleCreateBill} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Patient Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Patient *
                    </label>
                    <select
                      value={billFormData.patient}
                      onChange={(e) => handleBillFormChange('patient', e.target.value)}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select Patient</option>
                      {patients.map((patient) => (
                        <option key={patient._id} value={patient._id}>
                          {patient.firstName} {patient.lastName} - {patient.patientId}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Appointment Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Related Appointment (Optional)
                    </label>
                    <select
                      value={billFormData.appointment}
                      onChange={(e) => handleBillFormChange('appointment', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select Appointment</option>
                      {appointments.map((appointment) => (
                        <option key={appointment._id} value={appointment._id}>
                          {appointment.appointmentId} - {new Date(appointment.appointmentDate).toLocaleDateString()}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Due Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Due Date *
                    </label>
                    <input
                      type="date"
                      value={billFormData.dueDate}
                      onChange={(e) => handleBillFormChange('dueDate', e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Bill Items */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Bill Items</h3>
                    <button
                      type="button"
                      onClick={addBillItem}
                      className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm flex items-center space-x-1"
                    >
                      <Plus size={16} />
                      <span>Add Item</span>
                    </button>
                  </div>

                  <div className="space-y-4">
                    {billFormData.items.map((item, index) => (
                      <div key={index} className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border border-gray-200 rounded-lg">
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Description *
                          </label>
                          <input
                            type="text"
                            value={item.description}
                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                            required
                            className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Service or item description"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Category *
                          </label>
                          <select
                            value={item.category}
                            onChange={(e) => handleItemChange(index, 'category', e.target.value)}
                            required
                            className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="Consultation">Consultation</option>
                            <option value="Procedure">Procedure</option>
                            <option value="Medication">Medication</option>
                            <option value="Laboratory">Laboratory</option>
                            <option value="Imaging">Imaging</option>
                            <option value="Room Charges">Room Charges</option>
                            <option value="Other">Other</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Quantity *
                          </label>
                          <input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 1)}
                            min="1"
                            required
                            className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Unit Price *
                          </label>
                          <input
                            type="number"
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                            min="0"
                            step="0.01"
                            required
                            className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>

                        <div className="flex items-end">
                          <div className="w-full">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Total
                            </label>
                            <div className="w-full border border-gray-300 rounded px-3 py-2 bg-gray-50">
                              ${calculateItemTotal(item.quantity, item.unitPrice).toFixed(2)}
                            </div>
                          </div>
                          {billFormData.items.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeBillItem(index)}
                              className="ml-2 p-2 text-red-500 hover:text-red-700"
                            >
                              <X size={16} />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Tax and Discount */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tax Amount
                    </label>
                    <input
                      type="number"
                      value={billFormData.tax}
                      onChange={(e) => handleBillFormChange('tax', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Discount Amount
                    </label>
                    <input
                      type="number"
                      value={billFormData.discount}
                      onChange={(e) => handleBillFormChange('discount', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Total Amount
                    </label>
                    <div className="w-full border border-gray-300 rounded-lg px-3 py-2 bg-gray-50 text-lg font-semibold">
                      ${calculateBillTotal().toFixed(2)}
                    </div>
                  </div>
                </div>

                {/* Insurance Information */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Insurance Information (Optional)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Insurance Provider
                      </label>
                      <input
                        type="text"
                        value={billFormData.insurance.provider}
                        onChange={(e) => handleBillFormChange('insurance', { ...billFormData.insurance, provider: e.target.value })}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Insurance company name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Policy Number
                      </label>
                      <input
                        type="text"
                        value={billFormData.insurance.policyNumber}
                        onChange={(e) => handleBillFormChange('insurance', { ...billFormData.insurance, policyNumber: e.target.value })}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Policy number"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Claim Amount
                      </label>
                      <input
                        type="number"
                        value={billFormData.insurance.claimAmount}
                        onChange={(e) => handleBillFormChange('insurance', { ...billFormData.insurance, claimAmount: parseFloat(e.target.value) || 0 })}
                        min="0"
                        step="0.01"
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Notes
                  </label>
                  <textarea
                    value={billFormData.notes}
                    onChange={(e) => handleBillFormChange('notes', e.target.value)}
                    rows={3}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Any additional notes or instructions..."
                  />
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateBillModal(false);
                      resetBillForm();
                    }}
                    className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={formLoading}
                    className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-blue-300 flex items-center space-x-2"
                  >
                    {formLoading && <Loader className="animate-spin" size={16} />}
                    <span>Create Invoice</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}