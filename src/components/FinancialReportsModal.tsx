import React, { useState } from 'react';
import { X, Download, Calendar, Filter, FileText, BarChart3, Pie<PERSON><PERSON>, TrendingUp } from 'lucide-react';
import { financialAPI } from '../services/apiService';

interface FinancialReportsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const REPORT_TYPES = [
  {
    id: 'Revenue Report',
    name: 'Revenue Report',
    description: 'Comprehensive revenue analysis with department and service breakdowns',
    icon: TrendingUp,
    color: 'text-green-600 bg-green-50'
  },
  {
    id: 'Payment Report',
    name: 'Payment Report',
    description: 'Payment collection analysis by method, date, and transaction details',
    icon: BarChart3,
    color: 'text-blue-600 bg-blue-50'
  },
  {
    id: 'Outstanding Report',
    name: 'Outstanding Report',
    description: 'Accounts receivable aging and outstanding balance analysis',
    icon: FileText,
    color: 'text-yellow-600 bg-yellow-50'
  },
  {
    id: 'Department Revenue',
    name: 'Department Revenue',
    description: 'Department-wise revenue performance and comparison',
    icon: Pie<PERSON>hart,
    color: 'text-purple-600 bg-purple-50'
  },
  {
    id: 'Aging Report',
    name: 'Aging Report',
    description: 'Detailed accounts receivable aging with customer details',
    icon: Calendar,
    color: 'text-red-600 bg-red-50'
  }
];

const DEPARTMENTS = [
  'Emergency', 'Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics',
  'Gynecology', 'Oncology', 'Radiology', 'Laboratory', 'Pharmacy',
  'Surgery', 'ICU', 'OPD', 'IPD'
];

const PAYMENT_STATUSES = ['Pending', 'Partial', 'Paid', 'Overdue', 'Cancelled'];

const BILL_TYPES = [
  'Patient Bill', 'Emergency Bill', 'Insurance Bill', 'Vendor Bill',
  'Supplier Bill', 'Employee Expense', 'Utility Bill'
];

export function FinancialReportsModal({ isOpen, onClose }: FinancialReportsModalProps) {
  const [selectedReport, setSelectedReport] = useState('Revenue Report');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [reportConfig, setReportConfig] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    filters: {
      departments: [] as string[],
      paymentStatus: [] as string[],
      billTypes: [] as string[]
    },
    includeCharts: true,
    format: 'pdf' as 'pdf' | 'excel' | 'csv'
  });

  const handleFilterChange = (filterType: keyof typeof reportConfig.filters, value: string) => {
    setReportConfig(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [filterType]: prev.filters[filterType].includes(value)
          ? prev.filters[filterType].filter(item => item !== value)
          : [...prev.filters[filterType], value]
      }
    }));
  };

  const generateReport = async () => {
    setLoading(true);
    setError(null);

    try {
      const reportData = {
        reportType: selectedReport,
        startDate: reportConfig.startDate,
        endDate: reportConfig.endDate,
        filters: reportConfig.filters,
        includeCharts: reportConfig.includeCharts
      };

      if (reportConfig.format === 'pdf') {
        const response = await financialAPI.generateFinancialReport(reportData);
        
        if (response.ok) {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${selectedReport.replace(/\s+/g, '_')}_${reportConfig.startDate}_to_${reportConfig.endDate}.pdf`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        } else {
          throw new Error('Failed to generate report');
        }
      } else {
        // For Excel/CSV, we would need additional endpoints
        setError('Excel and CSV formats are coming soon');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const selectedReportInfo = REPORT_TYPES.find(report => report.id === selectedReport);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Financial Reports</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {/* Report Type Selection */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Select Report Type</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {REPORT_TYPES.map((report) => (
                <div
                  key={report.id}
                  onClick={() => setSelectedReport(report.id)}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedReport === report.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${report.color}`}>
                      <report.icon size={20} />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{report.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{report.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Date Range */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Date Range</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Start Date
                </label>
                <input
                  type="date"
                  value={reportConfig.startDate}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, startDate: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  End Date
                </label>
                <input
                  type="date"
                  value={reportConfig.endDate}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, endDate: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Filters */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Filters</h3>
            <div className="space-y-4">
              {/* Departments Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Departments
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {DEPARTMENTS.map((dept) => (
                    <label key={dept} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={reportConfig.filters.departments.includes(dept)}
                        onChange={() => handleFilterChange('departments', dept)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{dept}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Payment Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Status
                </label>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                  {PAYMENT_STATUSES.map((status) => (
                    <label key={status} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={reportConfig.filters.paymentStatus.includes(status)}
                        onChange={() => handleFilterChange('paymentStatus', status)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{status}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Bill Types Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Bill Types
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {BILL_TYPES.map((type) => (
                    <label key={type} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={reportConfig.filters.billTypes.includes(type)}
                        onChange={() => handleFilterChange('billTypes', type)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{type}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Report Options */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Report Options</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="includeCharts"
                  checked={reportConfig.includeCharts}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, includeCharts: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="includeCharts" className="text-sm text-gray-700">
                  Include charts and graphs
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Export Format
                </label>
                <select
                  value={reportConfig.format}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, format: e.target.value as 'pdf' | 'excel' | 'csv' }))}
                  className="w-full md:w-48 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="pdf">PDF</option>
                  <option value="excel">Excel (Coming Soon)</option>
                  <option value="csv">CSV (Coming Soon)</option>
                </select>
              </div>
            </div>
          </div>

          {/* Selected Report Preview */}
          {selectedReportInfo && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Report Preview</h4>
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg ${selectedReportInfo.color}`}>
                  <selectedReportInfo.icon size={16} />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">{selectedReportInfo.name}</p>
                  <p className="text-xs text-gray-600">{selectedReportInfo.description}</p>
                </div>
              </div>
              <div className="mt-3 text-xs text-gray-600">
                <p>Date Range: {reportConfig.startDate} to {reportConfig.endDate}</p>
                <p>Format: {reportConfig.format.toUpperCase()}</p>
                {reportConfig.filters.departments.length > 0 && (
                  <p>Departments: {reportConfig.filters.departments.join(', ')}</p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={generateReport}
            disabled={loading}
            className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Download size={16} />
            )}
            <span>{loading ? 'Generating...' : 'Generate Report'}</span>
          </button>
        </div>
      </div>
    </div>
  );
}
