import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Shield, AlertCircle } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  module?: string;
  action?: string;
  resource?: string;
  fallback?: React.ReactNode;
}

export function ProtectedRoute({
  children,
  module,
  action = 'view',
  resource,
  fallback
}: ProtectedRouteProps) {
  const { hasPermission, user, isAuthenticated } = useAuth();

  // If not authenticated, redirect to login
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />;
  }

  // If module is specified, check permissions
  if (module && !hasPermission(module, action, resource)) {
    return fallback || (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle size={64} className="text-red-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
          <p className="text-gray-500">
            You don't have permission to {action} {module} {resource ? `(${resource})` : ''}.
          </p>
          <p className="text-sm text-gray-400 mt-2">
            Contact your administrator to request access.
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}