import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Home,
  Users,
  Calendar,
  Stethoscope,
  TestTube,
  Pill,
  DollarSign,
  UserCheck,
  Building,
  Shield,
  BarChart3,
  Heart,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';

interface RoleBasedSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

const menuItems = [
  { id: 'dashboard', label: 'Dashboard', icon: Home, module: 'dashboard', path: '/dashboard' },
  { id: 'patients', label: 'Patient Management', icon: Users, module: 'patients', path: '/patients' },
  { id: 'appointments', label: 'Appointments', icon: Calendar, module: 'appointments', path: '/appointments' },
  { id: 'clinical', label: 'Clinical Management', icon: Stethoscope, module: 'clinical', path: '/clinical' },
  { id: 'laboratory', label: 'Laboratory', icon: TestTube, module: 'laboratory', path: '/laboratory' },
  { id: 'pharmacy', label: 'Pharmacy', icon: Pill, module: 'pharmacy', path: '/pharmacy' },
  { id: 'financial', label: 'Financial', icon: DollarSign, module: 'financial', path: '/financial' },
  { id: 'hr', label: 'Human Resources', icon: UserCheck, module: 'hr', path: '/hr' },
  { id: 'facility', label: 'Facility Management', icon: Building, module: 'facility', path: '/facility' },
  { id: 'admin', label: 'Administration', icon: Shield, module: 'admin', path: '/admin' },
  { id: 'reports', label: 'Reports & Analytics', icon: BarChart3, module: 'reports', path: '/reports' },
];

export function RoleBasedSidebar({ isOpen, onToggle }: RoleBasedSidebarProps) {
  const { user, hasPermission } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Filter menu items based on user permissions
  const accessibleMenuItems = menuItems.filter(item => 
    hasPermission(item.module, 'view')
  );

  return (
    <div className={`fixed left-0 top-0 h-full bg-white shadow-lg transition-all duration-300 z-50 ${isOpen ? 'w-64' : 'w-16'}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className={`flex items-center space-x-3 ${isOpen ? '' : 'justify-center'}`}>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Heart size={20} className="text-white" />
            </div>
            {isOpen && (
              <div>
                <h1 className="text-xl font-bold text-gray-900">MediCore HMS</h1>
                <p className="text-xs text-gray-500">Hospital Management System</p>
              </div>
            )}
          </div>
          {isOpen && (
            <button
              onClick={onToggle}
              className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ChevronLeft size={16} className="text-gray-600" />
            </button>
          )}
        </div>
      </div>

      {/* User Info */}
      {isOpen && user && (
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <Users size={20} className="text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-gray-900 truncate">
                {user.firstName} {user.lastName}
              </div>
              <div className="text-sm text-gray-500 truncate">{user.role.name}</div>
            </div>
          </div>
        </div>
      )}

      <nav className="mt-6">
        <div className="px-2 space-y-1">
          {accessibleMenuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;

            return (
              <button
                key={item.id}
                onClick={() => navigate(item.path)}
                className={`w-full flex items-center px-3 py-2 rounded-lg transition-colors ${
                  isActive
                    ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
                title={!isOpen ? item.label : undefined}
              >
                <Icon size={20} className={`${isActive ? 'text-blue-600' : 'text-gray-500'} ${isOpen ? 'mr-3' : ''}`} />
                {isOpen && <span className="font-medium">{item.label}</span>}
              </button>
            );
          })}
        </div>
      </nav>

      {!isOpen && (
        <div className="absolute bottom-4 left-2">
          <button
            onClick={onToggle}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronRight size={16} className="text-gray-600" />
          </button>
        </div>
      )}
    </div>
  );
}