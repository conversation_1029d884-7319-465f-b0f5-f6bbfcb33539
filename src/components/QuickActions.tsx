import React from 'react';
import { Plus, Calendar, FileText, Users, TestTube, Pill } from 'lucide-react';
import { useAuth } from '../context/AuthContext';

interface QuickActionsProps {
  onNavigate: (module: string) => void;
}

const createQuickActions = (onNavigate: (module: string) => void, hasPermission: (module: string, action: string) => boolean) => [
  {
    id: 1,
    label: 'Add New Patient',
    icon: Plus,
    color: 'bg-blue-500 hover:bg-blue-600',
    action: () => {
      onNavigate('patients');
      localStorage.setItem('pendingAction', 'add-patient');
    },
    permission: () => hasPermission('patients', 'create')
  },
  {
    id: 2,
    label: 'Schedule Appointment',
    icon: Calendar,
    color: 'bg-green-500 hover:bg-green-600',
    action: () => {
      onNavigate('appointments');
      localStorage.setItem('pendingAction', 'add-appointment');
    },
    permission: () => hasPermission('clinical', 'create')
  },
  {
    id: 3,
    label: 'Create Report',
    icon: FileText,
    color: 'bg-purple-500 hover:bg-purple-600',
    action: () => onNavigate('reports'),
    permission: () => hasPermission('reports', 'create')
  },
  {
    id: 4,
    label: 'View Staff',
    icon: Users,
    color: 'bg-orange-500 hover:bg-orange-600',
    action: () => onNavigate('hr'),
    permission: () => hasPermission('hr', 'view')
  },
  {
    id: 5,
    label: 'Lab Orders',
    icon: TestTube,
    color: 'bg-teal-500 hover:bg-teal-600',
    action: () => onNavigate('laboratory'),
    permission: () => hasPermission('laboratory', 'create')
  },
  {
    id: 6,
    label: 'Pharmacy',
    icon: Pill,
    color: 'bg-pink-500 hover:bg-pink-600',
    action: () => onNavigate('pharmacy'),
    permission: () => hasPermission('pharmacy', 'view')
  }
];

export function QuickActions({ onNavigate }: QuickActionsProps) {
  const { hasPermission } = useAuth();
  const quickActions = createQuickActions(onNavigate, hasPermission);

  // Filter actions based on user permissions
  const accessibleActions = quickActions.filter(action => action.permission());

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>

      {accessibleActions.length > 0 ? (
        <div className="grid grid-cols-2 gap-3">
          {accessibleActions.map((action) => {
          const Icon = action.icon;
          return (
            <button
              key={action.id}
              onClick={action.action}
              className={`p-4 rounded-lg text-white transition-colors ${action.color} group hover:shadow-lg transform hover:scale-105`}
            >
              <div className="flex flex-col items-center space-y-2">
                <Icon size={24} className="group-hover:scale-110 transition-transform" />
                <span className="text-sm font-medium text-center">{action.label}</span>
              </div>
            </button>
          );
        })}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No quick actions available based on your permissions.</p>
          <p className="text-sm mt-1">Contact your administrator for access.</p>
        </div>
      )}
    </div>
  );
}