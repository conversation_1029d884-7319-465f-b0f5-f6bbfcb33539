import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, Save, Calculator } from 'lucide-react';
import { financialAPI, patientAPI } from '../services/apiService';

interface BillItem {
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  category: string;
  discount?: number;
  taxRate?: number;
  taxAmount?: number;
}

interface UniversalBillModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const BILL_TYPES = [
  // Patient Bills
  'Patient Bill', 'Emergency Bill', 'Insurance Bill', 'Corporate Bill', 
  'Government Bill', 'Charity Bill', 'Cash Bill', 'Credit Bill',
  
  // Vendor/Supplier Bills
  'Vendor Bill', 'Supplier Bill', 'Utility Bill', 'Service Bill',
  'Equipment Bill', 'Maintenance Bill', 'Rental Bill',
  
  // Internal Bills
  'Employee Expense', 'Payroll', 'Petty Cash', 'Advance Payment',
  'Reimbursement', 'Inter-Department Transfer',
  
  // Other
  'Miscellaneous'
];

const ITEM_CATEGORIES = [
  // Patient Services
  'Consultation', 'Lab Test', 'Imaging', 'Medication', 'Surgery', 'Room Charges', 
  'Emergency', 'Therapy', 'Equipment', 'Nursing Care', 'ICU Charges', 'OT Charges',
  'Physiotherapy', 'Dialysis', 'Blood Bank', 'Ambulance', 'Vaccination',
  
  // Administrative Services
  'Registration Fee', 'Medical Records', 'Certificate Fee', 'Report Fee',
  
  // Vendor/Supplier Bills
  'Medical Supplies', 'Pharmaceutical Supplies', 'Equipment Purchase', 'Equipment Maintenance',
  'Utilities', 'Facility Maintenance', 'Cleaning Services', 'Security Services',
  'IT Services', 'Legal Services', 'Consulting Services', 'Insurance Premium',
  
  // Employee Related
  'Salary', 'Overtime', 'Bonus', 'Travel Allowance', 'Medical Allowance',
  'Training Expenses', 'Recruitment Expenses',
  
  // Other
  'Other'
];

export function UniversalBillModal({ isOpen, onClose, onSuccess }: UniversalBillModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [patients, setPatients] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);

  const [formData, setFormData] = useState({
    billType: 'Patient Bill',
    patient: '',
    vendor: {
      name: '',
      contactPerson: '',
      phone: '',
      email: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: ''
      },
      taxId: ''
    },
    employee: '',
    items: [
      {
        description: '',
        quantity: 1,
        unitPrice: 0,
        totalPrice: 0,
        category: 'Consultation',
        discount: 0,
        taxRate: 0,
        taxAmount: 0
      }
    ] as BillItem[],
    subtotal: 0,
    taxAmount: 0,
    discountAmount: 0,
    totalAmount: 0,
    notes: '',
    dueDate: '',
    costCenter: '',
    budgetCode: '',
    projectCode: '',
    poNumber: '',
    invoiceNumber: ''
  });

  useEffect(() => {
    if (isOpen) {
      fetchPatients();
      fetchEmployees();
      // Set default due date to 30 days from now
      const defaultDueDate = new Date();
      defaultDueDate.setDate(defaultDueDate.getDate() + 30);
      setFormData(prev => ({
        ...prev,
        dueDate: defaultDueDate.toISOString().split('T')[0]
      }));
    }
  }, [isOpen]);

  useEffect(() => {
    calculateTotals();
  }, [formData.items, formData.discountAmount]);

  const fetchPatients = async () => {
    try {
      const response = await patientAPI.getPatients({ limit: 100 });
      if (response.success) {
        setPatients(response.data);
      }
    } catch (error) {
      console.error('Error fetching patients:', error);
    }
  };

  const fetchEmployees = async () => {
    try {
      // Assuming there's an employee API endpoint
      // const response = await employeeAPI.getEmployees({ limit: 100 });
      // if (response.success) {
      //   setEmployees(response.data);
      // }
    } catch (error) {
      console.error('Error fetching employees:', error);
    }
  };

  const calculateTotals = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + item.totalPrice, 0);
    const totalTax = formData.items.reduce((sum, item) => sum + (item.taxAmount || 0), 0);
    const totalAmount = subtotal + totalTax - formData.discountAmount;

    setFormData(prev => ({
      ...prev,
      subtotal,
      taxAmount: totalTax,
      totalAmount
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [
        ...prev.items,
        {
          description: '',
          quantity: 1,
          unitPrice: 0,
          totalPrice: 0,
          category: 'Consultation',
          discount: 0,
          taxRate: 0,
          taxAmount: 0
        }
      ]
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      setFormData(prev => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index)
      }));
    }
  };

  const updateItem = (index: number, field: keyof BillItem, value: any) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], [field]: value };
      
      // Recalculate item totals
      if (field === 'quantity' || field === 'unitPrice' || field === 'discount' || field === 'taxRate') {
        const item = newItems[index];
        const baseTotal = item.quantity * item.unitPrice;
        const discountAmount = item.discount || 0;
        const taxAmount = ((baseTotal - discountAmount) * (item.taxRate || 0)) / 100;
        
        newItems[index] = {
          ...item,
          totalPrice: baseTotal - discountAmount,
          taxAmount
        };
      }
      
      return { ...prev, items: newItems };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const billData = {
        ...formData,
        vendor: formData.billType.includes('Vendor') || formData.billType.includes('Supplier') || formData.billType.includes('Utility') || formData.billType.includes('Service') ? formData.vendor : undefined,
        patient: formData.billType === 'Patient Bill' || formData.billType === 'Emergency Bill' || formData.billType === 'Insurance Bill' ? formData.patient : undefined,
        employee: formData.billType.includes('Employee') || formData.billType === 'Payroll' || formData.billType === 'Reimbursement' ? formData.employee : undefined
      };

      const response = await financialAPI.createUniversalBill(billData);

      if (response.success) {
        onSuccess();
        onClose();
        resetForm();
      } else {
        setError(response.error || 'Failed to create bill');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create bill');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      billType: 'Patient Bill',
      patient: '',
      vendor: {
        name: '',
        contactPerson: '',
        phone: '',
        email: '',
        address: {
          street: '',
          city: '',
          state: '',
          zipCode: '',
          country: ''
        },
        taxId: ''
      },
      employee: '',
      items: [
        {
          description: '',
          quantity: 1,
          unitPrice: 0,
          totalPrice: 0,
          category: 'Consultation',
          discount: 0,
          taxRate: 0,
          taxAmount: 0
        }
      ],
      subtotal: 0,
      taxAmount: 0,
      discountAmount: 0,
      totalAmount: 0,
      notes: '',
      dueDate: '',
      costCenter: '',
      budgetCode: '',
      projectCode: '',
      poNumber: '',
      invoiceNumber: ''
    });
    setError(null);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Create Universal Bill</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {/* Bill Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bill Type *
            </label>
            <select
              value={formData.billType}
              onChange={(e) => setFormData(prev => ({ ...prev, billType: e.target.value }))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              {BILL_TYPES.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          {/* Conditional Fields Based on Bill Type */}
          {(formData.billType === 'Patient Bill' || formData.billType === 'Emergency Bill' || formData.billType === 'Insurance Bill') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Patient *
              </label>
              <select
                value={formData.patient}
                onChange={(e) => setFormData(prev => ({ ...prev, patient: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select Patient</option>
                {patients.map(patient => (
                  <option key={patient._id} value={patient._id}>
                    {patient.firstName} {patient.lastName} - {patient.patientId}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Vendor Information */}
          {(formData.billType.includes('Vendor') || formData.billType.includes('Supplier') || formData.billType.includes('Utility') || formData.billType.includes('Service')) && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Vendor Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Vendor Name *
                  </label>
                  <input
                    type="text"
                    value={formData.vendor.name}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      vendor: { ...prev.vendor, name: e.target.value }
                    }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Person
                  </label>
                  <input
                    type="text"
                    value={formData.vendor.contactPerson}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      vendor: { ...prev.vendor, contactPerson: e.target.value }
                    }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone
                  </label>
                  <input
                    type="tel"
                    value={formData.vendor.phone}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      vendor: { ...prev.vendor, phone: e.target.value }
                    }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    value={formData.vendor.email}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      vendor: { ...prev.vendor, email: e.target.value }
                    }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Employee Selection */}
          {(formData.billType.includes('Employee') || formData.billType === 'Payroll' || formData.billType === 'Reimbursement') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Employee *
              </label>
              <select
                value={formData.employee}
                onChange={(e) => setFormData(prev => ({ ...prev, employee: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select Employee</option>
                {employees.map(employee => (
                  <option key={employee._id} value={employee._id}>
                    {employee.firstName} {employee.lastName} - {employee.department}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Items Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Items & Services</h3>
              <button
                type="button"
                onClick={addItem}
                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-lg flex items-center space-x-1 text-sm"
              >
                <Plus size={16} />
                <span>Add Item</span>
              </button>
            </div>

            <div className="space-y-3">
              {formData.items.map((item, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Description *
                      </label>
                      <input
                        type="text"
                        value={item.description}
                        onChange={(e) => updateItem(index, 'description', e.target.value)}
                        className="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Category
                      </label>
                      <select
                        value={item.category}
                        onChange={(e) => updateItem(index, 'category', e.target.value)}
                        className="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      >
                        {ITEM_CATEGORIES.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Quantity
                      </label>
                      <input
                        type="number"
                        min="1"
                        value={item.quantity}
                        onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                        className="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Unit Price
                      </label>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={item.unitPrice}
                        onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                        className="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="flex items-end">
                      <div className="flex-1">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Total
                        </label>
                        <div className="text-sm font-medium text-gray-900 py-1">
                          ${item.totalPrice.toFixed(2)}
                        </div>
                      </div>
                      {formData.items.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeItem(index)}
                          className="ml-2 text-red-500 hover:text-red-700"
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Due Date
              </label>
              <input
                type="date"
                value={formData.dueDate}
                onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Discount Amount
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.discountAmount}
                onChange={(e) => setFormData(prev => ({ ...prev, discountAmount: parseFloat(e.target.value) || 0 }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                PO Number
              </label>
              <input
                type="text"
                value={formData.poNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, poNumber: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cost Center
              </label>
              <input
                type="text"
                value={formData.costCenter}
                onChange={(e) => setFormData(prev => ({ ...prev, costCenter: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Additional notes or comments..."
            />
          </div>

          {/* Totals Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-lg font-medium text-gray-900 mb-3">Bill Summary</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Subtotal:</span>
                <span className="text-sm font-medium">${formData.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Tax:</span>
                <span className="text-sm font-medium">${formData.taxAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Discount:</span>
                <span className="text-sm font-medium">-${formData.discountAmount.toFixed(2)}</span>
              </div>
              <div className="border-t border-gray-200 pt-2">
                <div className="flex justify-between">
                  <span className="text-lg font-bold text-gray-900">Total Amount:</span>
                  <span className="text-lg font-bold text-gray-900">${formData.totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save size={16} />
              )}
              <span>{loading ? 'Creating...' : 'Create Bill'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
