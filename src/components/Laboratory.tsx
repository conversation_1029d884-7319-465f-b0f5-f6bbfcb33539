import React, { useState, useEffect } from 'react';
import {
  TestTube,
  Search,
  Filter,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  FileText,
  Download,
  Loader,
  Eye,
  Edit
} from 'lucide-react';
import { laboratoryAPI } from '../services/apiService';

interface LabTest {
  _id: string;
  testId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
  orderedBy: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  testName: string;
  testCategory: string;
  testType: string;
  orderDate: string;
  sampleCollectionDate?: string;
  expectedResultDate?: string;
  resultDate?: string;
  status: string;
  priority: string;
  results?: {
    values: Array<{
      parameter: string;
      value: string;
      unit: string;
      referenceRange: string;
      status: string;
    }>;
    interpretation: string;
    technician: string;
  };
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export function Laboratory() {
  const [labTests, setLabTests] = useState<LabTest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedTest, setSelectedTest] = useState<LabTest | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch lab tests from API
  useEffect(() => {
    fetchLabTests();
  }, [currentPage, searchTerm, statusFilter, categoryFilter]);

  const fetchLabTests = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        limit: 10
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }

      if (categoryFilter !== 'all') {
        params.category = categoryFilter;
      }

      const response = await laboratoryAPI.getTests(params);

      if (response.success) {
        setLabTests(response.data);
        setTotalPages(response.pagination?.pages || 1);
        setError(null);
      } else {
        setError('Failed to fetch lab tests');
      }
    } catch (err) {
      console.error('Error fetching lab tests:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch lab tests');
    } finally {
      setLoading(false);
    }
  };

  const updateTestStatus = async (testId: string, newStatus: string) => {
    try {
      await laboratoryAPI.updateTest(testId, { status: newStatus });
      fetchLabTests();
    } catch (err) {
      console.error('Error updating test status:', err);
    }
  };
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'ordered': return 'bg-blue-100 text-blue-800';
      case 'sample collected': return 'bg-yellow-100 text-yellow-800';
      case 'in progress': return 'bg-orange-100 text-orange-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'urgent': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'normal': return 'text-green-600';
      case 'low': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Laboratory Management</h1>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <Plus size={20} />
          <span>Order New Test</span>
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search tests by patient name, test name, or test ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex gap-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="Ordered">Ordered</option>
              <option value="Sample Collected">Sample Collected</option>
              <option value="In Progress">In Progress</option>
              <option value="Completed">Completed</option>
              <option value="Cancelled">Cancelled</option>
            </select>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Categories</option>
              <option value="Hematology">Hematology</option>
              <option value="Biochemistry">Biochemistry</option>
              <option value="Microbiology">Microbiology</option>
              <option value="Immunology">Immunology</option>
              <option value="Pathology">Pathology</option>
            </select>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="bg-white rounded-lg shadow p-8">
          <div className="flex items-center justify-center">
            <Loader className="animate-spin h-8 w-8 text-blue-500" />
            <span className="ml-2 text-gray-600">Loading lab tests...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center text-red-600">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>{error}</span>
          </div>
          <button
            onClick={fetchLabTests}
            className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
          >
            Retry
          </button>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="bg-white rounded-lg shadow p-8">
          <div className="flex items-center justify-center">
            <Loader className="animate-spin h-8 w-8 text-blue-500" />
            <span className="ml-2 text-gray-600">Loading lab tests...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center text-red-600">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>{error}</span>
          </div>
          <button
            onClick={fetchLabTests}
            className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
          >
            Retry
          </button>
        </div>
      )}

      {/* Lab Tests List */}
      {!loading && !error && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Lab Tests ({labTests.length})
            </h2>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Test Info</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Patient</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Ordered By</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Dates</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Priority</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {labTests.map((test) => (
                  <tr key={test._id} className="hover:bg-gray-50">
                    <td className="py-4 px-6">
                      <div>
                        <div className="font-medium text-gray-900">{test.testId}</div>
                        <div className="text-sm text-gray-500">{test.testName}</div>
                        <div className="text-sm text-gray-500">{test.testCategory}</div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div>
                        <div className="font-medium text-gray-900">
                          {test.patient?.firstName} {test.patient?.lastName}
                        </div>
                        <div className="text-sm text-gray-500">{test.patient?.patientId}</div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div>
                        <div className="font-medium text-gray-900">
                          Dr. {test.orderedBy?.firstName} {test.orderedBy?.lastName}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div>
                        <div className="text-sm text-gray-900">Ordered: {formatDate(test.orderDate)}</div>
                        {test.sampleCollectionDate && (
                          <div className="text-sm text-gray-500">Collected: {formatDate(test.sampleCollectionDate)}</div>
                        )}
                        {test.expectedResultDate && (
                          <div className="text-sm text-gray-500">Expected: {formatDate(test.expectedResultDate)}</div>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(test.status)}`}>
                        {test.status}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`text-sm font-medium ${getPriorityColor(test.priority)}`}>
                        {test.priority || 'Normal'}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setSelectedTest(test)}
                          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                          title="View Details"
                        >
                          <Eye size={16} className="text-gray-500" />
                        </button>
                        {test.status === 'Ordered' && (
                          <button
                            onClick={() => updateTestStatus(test._id, 'Sample Collected')}
                            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                            title="Mark Sample Collected"
                          >
                            <CheckCircle size={16} className="text-green-500" />
                          </button>
                        )}
                        {test.status === 'Sample Collected' && (
                          <button
                            onClick={() => updateTestStatus(test._id, 'In Progress')}
                            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                            title="Start Processing"
                          >
                            <Clock size={16} className="text-blue-500" />
                          </button>
                        )}
                        {test.results && (
                          <button
                            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                            title="Download Results"
                          >
                            <Download size={16} className="text-purple-500" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      )}
      {/* Test Details Modal */}
      {selectedTest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Test Details</h2>
                <button
                  onClick={() => setSelectedTest(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Information</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Test ID</label>
                      <p className="text-gray-900">{selectedTest.testId}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Test Name</label>
                      <p className="text-gray-900">{selectedTest.testName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Category</label>
                      <p className="text-gray-900">{selectedTest.testCategory}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Type</label>
                      <p className="text-gray-900">{selectedTest.testType}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Status</label>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(selectedTest.status)}`}>
                        {selectedTest.status}
                      </span>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Priority</label>
                      <span className={`text-sm font-medium ${getPriorityColor(selectedTest.priority)}`}>
                        {selectedTest.priority || 'Normal'}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Patient & Doctor</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Patient</label>
                      <p className="text-gray-900">
                        {selectedTest.patient?.firstName} {selectedTest.patient?.lastName}
                      </p>
                      <p className="text-sm text-gray-500">{selectedTest.patient?.patientId}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Ordered By</label>
                      <p className="text-gray-900">
                        Dr. {selectedTest.orderedBy?.firstName} {selectedTest.orderedBy?.lastName}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Order Date</label>
                      <p className="text-gray-900">{formatDateTime(selectedTest.orderDate)}</p>
                    </div>
                    {selectedTest.sampleCollectionDate && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Sample Collection Date</label>
                        <p className="text-gray-900">{formatDateTime(selectedTest.sampleCollectionDate)}</p>
                      </div>
                    )}
                    {selectedTest.expectedResultDate && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Expected Result Date</label>
                        <p className="text-gray-900">{formatDateTime(selectedTest.expectedResultDate)}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {selectedTest.results && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Results</h3>
                  <div className="space-y-4">
                    {selectedTest.results.values?.map((result, index) => (
                      <div key={index} className="grid grid-cols-4 gap-4 p-3 bg-gray-50 rounded-lg">
                        <div>
                          <label className="text-sm font-medium text-gray-500">Parameter</label>
                          <p className="text-gray-900">{result.parameter}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Value</label>
                          <p className="text-gray-900">{result.value} {result.unit}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Reference Range</label>
                          <p className="text-gray-900">{result.referenceRange}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Status</label>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            result.status === 'Normal' ? 'bg-green-100 text-green-800' :
                            result.status === 'High' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {result.status}
                          </span>
                        </div>
                      </div>
                    ))}
                    {selectedTest.results.interpretation && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Interpretation</label>
                        <p className="text-gray-900 mt-1">{selectedTest.results.interpretation}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex space-x-4">
                  <button
                    onClick={() => setSelectedTest(null)}
                    className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
                  >
                    Close
                  </button>
                  {selectedTest.results && (
                    <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center">
                      <Download className="h-4 w-4 mr-2" />
                      Download Results
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}