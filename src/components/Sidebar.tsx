import React from 'react';
import { 
  Home, 
  Users, 
  Stethoscope, 
  TestTube, 
  Pill, 
  DollarSign, 
  UserCheck, 
  Building, 
  Shield, 
  BarChart3,
  Heart,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { ActiveModule } from '../App';

interface SidebarProps {
  isOpen: boolean;
  activeModule: ActiveModule;
  onModuleChange: (module: ActiveModule) => void;
  onToggle: () => void;
}

const menuItems = [
  { id: 'dashboard', label: 'Dashboard', icon: Home },
  { id: 'patients', label: 'Patient Management', icon: Users },
  { id: 'clinical', label: 'Clinical Management', icon: Stethoscope },
  { id: 'laboratory', label: 'Laboratory', icon: TestTube },
  { id: 'pharmacy', label: 'Pharmacy', icon: Pill },
  { id: 'financial', label: 'Financial', icon: DollarSign },
  { id: 'hr', label: 'Human Resources', icon: UserCheck },
  { id: 'facility', label: 'Facility Management', icon: Building },
  { id: 'admin', label: 'Administration', icon: Shield },
  { id: 'reports', label: 'Reports & Analytics', icon: BarChart3 },
];

export function Sidebar({ isOpen, activeModule, onModuleChange, onToggle }: SidebarProps) {
  return (
    <div className={`fixed left-0 top-0 h-full bg-gradient-to-b from-slate-900 to-slate-800 shadow-2xl transition-all duration-300 z-50 ${isOpen ? 'w-64' : 'w-16'}`}>
      <div className="p-4 border-b border-slate-700">
        <div className="flex items-center justify-between">
          <div className={`flex items-center space-x-3 ${isOpen ? '' : 'justify-center'}`}>
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <Heart size={22} className="text-white" />
            </div>
            {isOpen && (
              <div>
                <h1 className="text-xl font-bold text-white">MediCore HMS</h1>
                <p className="text-xs text-slate-300">Hospital Management System</p>
              </div>
            )}
          </div>
          {isOpen && (
            <button
              onClick={onToggle}
              className="p-2 hover:bg-slate-700 rounded-lg transition-colors text-slate-300 hover:text-white"
            >
              <ChevronLeft size={16} />
            </button>
          )}
        </div>
      </div>

      <nav className="mt-6">
        <div className="px-3 space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeModule === item.id;

            return (
              <button
                key={item.id}
                onClick={() => onModuleChange(item.id as ActiveModule)}
                className={`w-full flex items-center px-4 py-3 rounded-xl transition-all duration-200 group ${
                  isActive
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105'
                    : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                }`}
              >
                <Icon size={20} className={`${isActive ? 'text-white' : 'text-slate-400 group-hover:text-white'} ${isOpen ? 'mr-3' : ''} transition-colors`} />
                {isOpen && <span className="font-medium text-sm">{item.label}</span>}
                {isActive && isOpen && (
                  <div className="ml-auto w-2 h-2 bg-white rounded-full opacity-75"></div>
                )}
              </button>
            );
          })}
        </div>
      </nav>

      {!isOpen && (
        <div className="absolute bottom-4 left-2">
          <button
            onClick={onToggle}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors text-slate-400 hover:text-white"
          >
            <ChevronRight size={16} />
          </button>
        </div>
      )}

      {/* Footer */}
      {isOpen && (
        <div className="absolute bottom-4 left-4 right-4">
          <div className="text-xs text-slate-400 text-center">
            <p>© 2025 MediCore HMS</p>
            <p className="mt-1">v2.0.0</p>
          </div>
        </div>
      )}
    </div>
  );
}