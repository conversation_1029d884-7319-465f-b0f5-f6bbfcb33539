import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, Loader } from 'lucide-react';
import { appointmentAPI } from '../services/apiService';

export function AppointmentSchedule() {
  const [appointments, setAppointments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTodaysAppointments();
  }, []);

  const fetchTodaysAppointments = async () => {
    try {
      setLoading(true);
      const today = new Date().toISOString().split('T')[0];
      const response = await appointmentAPI.getAll({
        date: today,
        limit: 5,
        status: 'Scheduled,Confirmed'
      });

      if (response.success) {
        setAppointments(response.data);
        setError(null);
      } else {
        setError('Failed to fetch appointments');
      }
    } catch (err) {
      console.error('Error fetching today\'s appointments:', err);
      setError('Failed to fetch appointments');
      // Set fallback data
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Confirmed': return 'bg-green-100 text-green-800';
      case 'Scheduled': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-yellow-100 text-yellow-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Cancelled': return 'bg-red-100 text-red-800';
      case 'No Show': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Today's Schedule</h2>
        <Calendar size={20} className="text-gray-500" />
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader className="animate-spin" size={24} />
          <span className="ml-2 text-gray-500">Loading appointments...</span>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500 text-sm">{error}</p>
          <button
            onClick={fetchTodaysAppointments}
            className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Try Again
          </button>
        </div>
      ) : appointments.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 text-sm">No appointments scheduled for today</p>
        </div>
      ) : (
        <div className="space-y-4">
          {appointments.map((appointment) => (
            <div key={appointment._id} className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Clock size={16} className="text-gray-500" />
                  <span className="text-sm font-medium text-gray-900">
                    {formatTime(appointment.appointmentTime)}
                  </span>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(appointment.status)}`}>
                  {appointment.status}
                </span>
              </div>

              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <User size={14} className="text-gray-400" />
                  <span className="text-sm font-medium text-gray-900">
                    {appointment.patient?.firstName} {appointment.patient?.lastName}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  Dr. {appointment.doctor?.firstName} {appointment.doctor?.lastName}
                </div>
                <div className="text-xs text-gray-400">{appointment.appointmentType}</div>
              </div>
            </div>
          ))}
        </div>
      )}

      <button
        onClick={() => window.location.href = '/appointments'}
        className="w-full mt-4 text-sm text-blue-600 hover:text-blue-800 font-medium"
      >
        View All Appointments
      </button>
    </div>
  );
}