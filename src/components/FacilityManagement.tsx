import React, { useState, useEffect } from 'react';
import {
  Building,
  Bed,
  Wrench,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  MapPin,
  Settings,
  Plus,
  Edit,
  Eye,
  X,
  Loader,
  Users,
  Activity,
  Search,
  Filter
} from 'lucide-react';
import { facilityAPI } from '../services/apiService';

export function FacilityManagement() {
  const [activeTab, setActiveTab] = useState('facilities');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // State for rooms
  const [rooms, setRooms] = useState<any[]>([]);
  const [equipment, setEquipment] = useState<any[]>([]);
  const [maintenanceRequests, setMaintenanceRequests] = useState<any[]>([]);
  const [facilityStats, setFacilityStats] = useState<any>(null);

  // Loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [showRoomModal, setShowRoomModal] = useState(false);
  const [showEquipmentModal, setShowEquipmentModal] = useState(false);
  const [showMaintenanceModal, setShowMaintenanceModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  // Form data
  const [roomFormData, setRoomFormData] = useState({
    roomNumber: '',
    roomType: 'Patient Room',
    floor: '',
    building: '',
    capacity: 1,
    amenities: [],
    dailyRate: 0,
    notes: ''
  });

  const [equipmentFormData, setEquipmentFormData] = useState({
    name: '',
    category: 'Medical Device',
    manufacturer: '',
    model: '',
    serialNumber: '',
    location: {
      building: '',
      floor: '',
      description: ''
    },
    purchasePrice: 0,
    condition: 'Good',
    notes: ''
  });

  const [maintenanceFormData, setMaintenanceFormData] = useState({
    type: 'Room',
    targetId: '',
    priority: 'Medium',
    category: 'Preventive',
    description: '',
    scheduledDate: '',
    estimatedCost: 0
  });

  const [formLoading, setFormLoading] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    fetchRooms();
    fetchEquipment();
    fetchMaintenanceRequests();
    fetchFacilityStats();
  }, []);

  const fetchRooms = async () => {
    try {
      setLoading(true);
      const response = await facilityAPI.getRooms();
      if (response.success) {
        setRooms(response.data);
      }
    } catch (err) {
      console.error('Error fetching rooms:', err);
      setError('Failed to fetch rooms');
    } finally {
      setLoading(false);
    }
  };

  const fetchEquipment = async () => {
    try {
      const response = await facilityAPI.getEquipment();
      if (response.success) {
        setEquipment(response.data);
      }
    } catch (err) {
      console.error('Error fetching equipment:', err);
    }
  };

  const fetchMaintenanceRequests = async () => {
    try {
      const response = await facilityAPI.getMaintenanceRequests();
      if (response.success) {
        setMaintenanceRequests(response.data);
      }
    } catch (err) {
      console.error('Error fetching maintenance requests:', err);
    }
  };

  const fetchFacilityStats = async () => {
    try {
      const response = await facilityAPI.getStats();
      if (response.success) {
        setFacilityStats(response.data);
      }
    } catch (err) {
      console.error('Error fetching facility stats:', err);
    }
  };

  const handleRoomFormChange = (field: string, value: any) => {
    setRoomFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleEquipmentFormChange = (field: string, value: any) => {
    setEquipmentFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleMaintenanceFormChange = (field: string, value: any) => {
    setMaintenanceFormData(prev => ({ ...prev, [field]: value }));
  };

  const resetRoomForm = () => {
    setRoomFormData({
      roomNumber: '',
      roomType: 'Patient Room',
      floor: '',
      building: '',
      capacity: 1,
      amenities: [],
      dailyRate: 0,
      notes: ''
    });
  };

  const resetEquipmentForm = () => {
    setEquipmentFormData({
      name: '',
      category: 'Medical Device',
      manufacturer: '',
      model: '',
      serialNumber: '',
      location: {
        building: '',
        floor: '',
        description: ''
      },
      purchasePrice: 0,
      condition: 'Good',
      notes: ''
    });
  };

  const resetMaintenanceForm = () => {
    setMaintenanceFormData({
      type: 'Room',
      targetId: '',
      priority: 'Medium',
      category: 'Preventive',
      description: '',
      scheduledDate: '',
      estimatedCost: 0
    });
  };

  const handleCreateRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setFormLoading(true);
      const response = await facilityAPI.createRoom(roomFormData);
      if (response.success) {
        await fetchRooms();
        await fetchFacilityStats();
        setShowRoomModal(false);
        resetRoomForm();
      } else {
        setError(response.error || 'Failed to create room');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create room');
    } finally {
      setFormLoading(false);
    }
  };

  const handleCreateEquipment = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setFormLoading(true);
      const response = await facilityAPI.createEquipment(equipmentFormData);
      if (response.success) {
        await fetchEquipment();
        setShowEquipmentModal(false);
        resetEquipmentForm();
      } else {
        setError(response.error || 'Failed to create equipment');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create equipment');
    } finally {
      setFormLoading(false);
    }
  };

  const handleCreateMaintenanceRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setFormLoading(true);
      const response = await facilityAPI.createMaintenanceRequest(maintenanceFormData);
      if (response.success) {
        await fetchMaintenanceRequests();
        setShowMaintenanceModal(false);
        resetMaintenanceForm();
      } else {
        setError(response.error || 'Failed to create maintenance request');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create maintenance request');
    } finally {
      setFormLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Available': return 'bg-green-100 text-green-800';
      case 'Occupied': return 'bg-yellow-100 text-yellow-800';
      case 'Under Maintenance': return 'bg-red-100 text-red-800';
      case 'Scheduled': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-orange-100 text-orange-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Confirmed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Facility Management</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowMaintenanceModal(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Calendar size={20} />
            <span>Schedule Maintenance</span>
          </button>
          <button
            onClick={() => setShowRoomModal(true)}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Plus size={20} />
            <span>Add Room</span>
          </button>
        </div>
      </div>

      {/* Facility Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Rooms</p>
              <p className="text-2xl font-bold text-gray-900">
                {facilityStats?.totalRooms || rooms.length}
              </p>
            </div>
            <Building size={24} className="text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Available Rooms</p>
              <p className="text-2xl font-bold text-gray-900">
                {facilityStats?.availableRooms || rooms.filter(r => r.status === 'Available').length}
              </p>
            </div>
            <CheckCircle size={24} className="text-green-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Under Maintenance</p>
              <p className="text-2xl font-bold text-gray-900">
                {facilityStats?.maintenanceRooms || rooms.filter(r => r.status === 'Maintenance').length}
              </p>
            </div>
            <Wrench size={24} className="text-red-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Equipment</p>
              <p className="text-2xl font-bold text-gray-900">
                {facilityStats?.totalEquipment || equipment.length}
              </p>
            </div>
            <Settings size={24} className="text-purple-500" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('facilities')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'facilities'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Facilities
            </button>
            <button
              onClick={() => setActiveTab('maintenance')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'maintenance'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Maintenance
            </button>
            <button
              onClick={() => setActiveTab('bookings')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'bookings'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Room Bookings
            </button>
            <button
              onClick={() => setActiveTab('assets')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'assets'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Asset Management
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'facilities' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Room Management</h2>
                <button
                  onClick={() => setShowRoomModal(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                >
                  <Plus size={16} />
                  <span>Add Room</span>
                </button>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader className="animate-spin" size={24} />
                  <span className="ml-2 text-gray-500">Loading rooms...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {rooms.map((room) => (
                    <div key={room._id} className="bg-gray-50 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                            <Building size={20} className="text-white" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900">Room {room.roomNumber}</h3>
                            <p className="text-sm text-gray-500">{room.roomType}</p>
                          </div>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(room.status)}`}>
                          {room.status}
                        </span>
                      </div>
                    
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center space-x-2">
                          <MapPin size={14} className="text-gray-400" />
                          <span className="text-gray-600">{room.building} - {room.floor}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Bed size={14} className="text-gray-400" />
                          <span className="text-gray-600">Capacity: {room.capacity}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Users size={14} className="text-gray-400" />
                          <span className="text-gray-600">Occupancy: {room.currentOccupancy || 0}/{room.capacity}</span>
                        </div>
                        {room.dailyRate && (
                          <div className="flex items-center space-x-2">
                            <Activity size={14} className="text-gray-400" />
                            <span className="text-gray-600">Rate: ${room.dailyRate}/day</span>
                          </div>
                        )}
                      </div>

                      {room.amenities && room.amenities.length > 0 && (
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <p className="text-sm text-gray-500 mb-2">Amenities:</p>
                          <div className="flex flex-wrap gap-1">
                            {room.amenities.map((amenity: string, index: number) => (
                              <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                                {amenity}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="mt-4 flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedItem(room);
                            // Could open edit modal here
                          }}
                          className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm"
                        >
                          <Edit size={14} className="inline mr-1" />
                          Edit
                        </button>
                        <button
                          onClick={() => {
                            setSelectedItem(room);
                            // Could open details modal here
                          }}
                          className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm"
                        >
                          <Eye size={14} className="inline mr-1" />
                          View
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'maintenance' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Maintenance Schedule</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Wrench size={16} />
                  <span>Upcoming & Active</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Facility</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Type</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Description</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Scheduled Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Technician</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Priority</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {maintenanceSchedule.map((maintenance) => (
                      <tr key={maintenance.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{maintenance.facilityName}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{maintenance.type}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{maintenance.description}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{maintenance.scheduledDate}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{maintenance.technician}</td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(maintenance.priority)}`}>
                            {maintenance.priority}
                          </span>
                        </td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(maintenance.status)}`}>
                            {maintenance.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'bookings' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Room Bookings</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Calendar size={16} />
                  <span>Today's Schedule</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Facility</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Patient</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Procedure</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Surgeon</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Time</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {roomBookings.map((booking) => (
                      <tr key={booking.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{booking.facilityName}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.patientName}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.procedure}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.surgeon}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.date}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.startTime} - {booking.endTime}</td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(booking.status)}`}>
                            {booking.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'assets' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Asset Management</h2>
              
              <div className="text-center py-12">
                <Settings size={64} className="text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Asset Tracking</h3>
                <p className="text-gray-500">Track and manage hospital equipment and assets.</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Room Creation Modal */}
      {showRoomModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Add New Room</h2>
                <button
                  onClick={() => {
                    setShowRoomModal(false);
                    resetRoomForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>

              <form onSubmit={handleCreateRoom} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Room Number *
                    </label>
                    <input
                      type="text"
                      value={roomFormData.roomNumber}
                      onChange={(e) => handleRoomFormChange('roomNumber', e.target.value)}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., 101, A-205"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Room Type *
                    </label>
                    <select
                      value={roomFormData.roomType}
                      onChange={(e) => handleRoomFormChange('roomType', e.target.value)}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="Patient Room">Patient Room</option>
                      <option value="ICU">ICU</option>
                      <option value="Operating Room">Operating Room</option>
                      <option value="Emergency Room">Emergency Room</option>
                      <option value="Laboratory">Laboratory</option>
                      <option value="Radiology">Radiology</option>
                      <option value="Pharmacy">Pharmacy</option>
                      <option value="Office">Office</option>
                      <option value="Storage">Storage</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Building *
                    </label>
                    <input
                      type="text"
                      value={roomFormData.building}
                      onChange={(e) => handleRoomFormChange('building', e.target.value)}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., Main Building, Wing A"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Floor *
                    </label>
                    <input
                      type="text"
                      value={roomFormData.floor}
                      onChange={(e) => handleRoomFormChange('floor', e.target.value)}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., Ground Floor, 1st Floor"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Capacity *
                    </label>
                    <input
                      type="number"
                      value={roomFormData.capacity}
                      onChange={(e) => handleRoomFormChange('capacity', parseInt(e.target.value) || 1)}
                      min="1"
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Daily Rate ($)
                    </label>
                    <input
                      type="number"
                      value={roomFormData.dailyRate}
                      onChange={(e) => handleRoomFormChange('dailyRate', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes
                  </label>
                  <textarea
                    value={roomFormData.notes}
                    onChange={(e) => handleRoomFormChange('notes', e.target.value)}
                    rows={3}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Any additional notes about this room..."
                  />
                </div>

                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowRoomModal(false);
                      resetRoomForm();
                    }}
                    className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={formLoading}
                    className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-blue-300 flex items-center space-x-2"
                  >
                    {formLoading && <Loader className="animate-spin" size={16} />}
                    <span>Create Room</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Maintenance Request Modal */}
      {showMaintenanceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Schedule Maintenance</h2>
                <button
                  onClick={() => {
                    setShowMaintenanceModal(false);
                    resetMaintenanceForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>

              <form onSubmit={handleCreateMaintenanceRequest} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Type *
                    </label>
                    <select
                      value={maintenanceFormData.type}
                      onChange={(e) => handleMaintenanceFormChange('type', e.target.value)}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="Room">Room</option>
                      <option value="Equipment">Equipment</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target *
                    </label>
                    <select
                      value={maintenanceFormData.targetId}
                      onChange={(e) => handleMaintenanceFormChange('targetId', e.target.value)}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select {maintenanceFormData.type}</option>
                      {maintenanceFormData.type === 'Room'
                        ? rooms.map((room) => (
                            <option key={room._id} value={room._id}>
                              Room {room.roomNumber} - {room.roomType}
                            </option>
                          ))
                        : equipment.map((eq) => (
                            <option key={eq._id} value={eq._id}>
                              {eq.name} - {eq.category}
                            </option>
                          ))
                      }
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority *
                    </label>
                    <select
                      value={maintenanceFormData.priority}
                      onChange={(e) => handleMaintenanceFormChange('priority', e.target.value)}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="Low">Low</option>
                      <option value="Medium">Medium</option>
                      <option value="High">High</option>
                      <option value="Emergency">Emergency</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Category *
                    </label>
                    <select
                      value={maintenanceFormData.category}
                      onChange={(e) => handleMaintenanceFormChange('category', e.target.value)}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="Preventive">Preventive</option>
                      <option value="Corrective">Corrective</option>
                      <option value="Emergency">Emergency</option>
                      <option value="Upgrade">Upgrade</option>
                      <option value="Inspection">Inspection</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Scheduled Date *
                    </label>
                    <input
                      type="date"
                      value={maintenanceFormData.scheduledDate}
                      onChange={(e) => handleMaintenanceFormChange('scheduledDate', e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      required
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Estimated Cost ($)
                    </label>
                    <input
                      type="number"
                      value={maintenanceFormData.estimatedCost}
                      onChange={(e) => handleMaintenanceFormChange('estimatedCost', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    value={maintenanceFormData.description}
                    onChange={(e) => handleMaintenanceFormChange('description', e.target.value)}
                    required
                    rows={4}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Describe the maintenance work required..."
                  />
                </div>

                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowMaintenanceModal(false);
                      resetMaintenanceForm();
                    }}
                    className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={formLoading}
                    className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-blue-300 flex items-center space-x-2"
                  >
                    {formLoading && <Loader className="animate-spin" size={16} />}
                    <span>Schedule Maintenance</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}