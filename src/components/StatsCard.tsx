import React from 'react';
import { DivideIcon as LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: LucideIcon;
  color: 'blue' | 'green' | 'purple' | 'emerald' | 'red' | 'yellow';
}

const colorClasses = {
  blue: 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 shadow-blue-100',
  green: 'bg-gradient-to-br from-green-50 to-green-100 text-green-600 shadow-green-100',
  purple: 'bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 shadow-purple-100',
  emerald: 'bg-gradient-to-br from-emerald-50 to-emerald-100 text-emerald-600 shadow-emerald-100',
  red: 'bg-gradient-to-br from-red-50 to-red-100 text-red-600 shadow-red-100',
  yellow: 'bg-gradient-to-br from-yellow-50 to-yellow-100 text-yellow-600 shadow-yellow-100',
};

export function StatsCard({ title, value, change, changeType, icon: Icon, color }: StatsCardProps) {
  const TrendIcon = changeType === 'increase' ? TrendingUp : TrendingDown;
  const changeColor = changeType === 'increase' ? 'text-green-600' : 'text-red-600';

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-slate-100 hover:border-slate-200 group">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="text-sm font-semibold text-slate-600 mb-2 uppercase tracking-wide">{title}</h3>
          <div className="text-3xl font-bold text-slate-900 mb-3 group-hover:text-slate-800 transition-colors">{value}</div>
          <div className={`flex items-center space-x-1 text-sm font-medium ${changeColor}`}>
            <TrendIcon size={16} />
            <span>{change} from last month</span>
          </div>
        </div>
        <div className={`p-4 rounded-xl shadow-lg ${colorClasses[color]} group-hover:scale-110 transition-transform duration-300`}>
          <Icon size={26} />
        </div>
      </div>
    </div>
  );
}