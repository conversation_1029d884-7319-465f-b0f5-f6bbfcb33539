import React, { useState } from 'react';
import { 
  Shield, 
  Users, 
  Key, 
  FileText, 
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  Lock,
  UserPlus,
  Edit
} from 'lucide-react';
import { UserManagement } from './UserManagement';
import { useAuth } from '../context/AuthContext';

export function EnhancedAdministration() {
  const [activeTab, setActiveTab] = useState('users');
  const { hasPermission } = useAuth();

  const auditLogs = [
    {
      id: 'A001',
      timestamp: '2024-01-15 09:15 AM',
      user: 'Dr. <PERSON>',
      action: 'Patient Record Updated',
      details: 'Updated patient PT001247 medical history',
      ipAddress: '************',
      status: 'Success'
    },
    {
      id: 'A002',
      timestamp: '2024-01-15 08:45 AM',
      user: 'Dr. <PERSON>',
      action: 'Surgery Scheduled',
      details: 'Scheduled cardiac surgery for patient PT001246',
      ipAddress: '************',
      status: 'Success'
    },
    {
      id: 'A003',
      timestamp: '2024-01-15 08:30 AM',
      user: 'Nurse <PERSON>',
      action: 'Medication Dispensed',
      details: 'Dispensed medication for patient PT001245',
      ipAddress: '************',
      status: 'Success'
    },
    {
      id: 'A004',
      timestamp: '2024-01-15 07:20 AM',
      user: 'Unknown User',
      action: 'Failed Login Attempt',
      details: 'Failed login attempt <NAME_EMAIL>',
      ipAddress: '*************',
      status: 'Failed'
    }
  ];

  const systemSettings = [
    {
      category: 'General',
      settings: [
        { name: 'Hospital Name', value: 'MediCore Hospital', type: 'text' },
        { name: 'Default Language', value: 'English', type: 'select' },
        { name: 'Time Zone', value: 'UTC-5', type: 'select' },
        { name: 'Date Format', value: 'MM/DD/YYYY', type: 'select' }
      ]
    },
    {
      category: 'Security',
      settings: [
        { name: 'Session Timeout', value: '30 minutes', type: 'select' },
        { name: 'Password Policy', value: 'Strong', type: 'select' },
        { name: 'Two-Factor Authentication', value: 'Enabled', type: 'toggle' },
        { name: 'Login Attempts', value: '3', type: 'number' }
      ]
    },
    {
      category: 'Notifications',
      settings: [
        { name: 'Email Notifications', value: 'Enabled', type: 'toggle' },
        { name: 'SMS Notifications', value: 'Enabled', type: 'toggle' },
        { name: 'System Alerts', value: 'Enabled', type: 'toggle' },
        { name: 'Maintenance Alerts', value: 'Enabled', type: 'toggle' }
      ]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Success': return 'bg-green-100 text-green-800';
      case 'Failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">System Administration</h1>
        <div className="flex space-x-3">
          {hasPermission('admin', 'create', 'users') && (
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
              <UserPlus size={20} />
              <span>Add User</span>
            </button>
          )}
          {hasPermission('admin', 'export', 'logs') && (
            <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
              <FileText size={20} />
              <span>Export Logs</span>
            </button>
          )}
        </div>
      </div>

      {/* Admin Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">24</p>
            </div>
            <Users size={24} className="text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">22</p>
            </div>
            <CheckCircle size={24} className="text-green-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Failed Logins</p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
            <AlertCircle size={24} className="text-red-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">System Health</p>
              <p className="text-2xl font-bold text-gray-900">98.5%</p>
            </div>
            <Shield size={24} className="text-purple-500" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('users')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'users'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              User Management
            </button>
            <button
              onClick={() => setActiveTab('audit')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'audit'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Audit Logs
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'settings'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              System Settings
            </button>
            <button
              onClick={() => setActiveTab('compliance')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'compliance'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Compliance
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'users' && <UserManagement />}

          {activeTab === 'audit' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Audit Trail</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Clock size={16} />
                  <span>Last 24 hours</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Timestamp</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">User</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Action</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Details</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">IP Address</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {auditLogs.map((log) => (
                      <tr key={log.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm text-gray-900">{log.timestamp}</td>
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{log.user}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{log.action}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{log.details}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{log.ipAddress}</td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(log.status)}`}>
                            {log.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">System Configuration</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Settings size={16} />
                  <span>Global Settings</span>
                </div>
              </div>

              <div className="space-y-8">
                {systemSettings.map((category) => (
                  <div key={category.category} className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">{category.category}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {category.settings.map((setting) => (
                        <div key={setting.name} className="flex items-center justify-between p-4 bg-white rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">{setting.name}</h4>
                            <p className="text-sm text-gray-500">{setting.value}</p>
                          </div>
                          {hasPermission('admin', 'edit', 'settings') && (
                            <button className="text-blue-600 hover:text-blue-800 text-sm">
                              Edit
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'compliance' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Compliance & Regulations</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Shield size={16} />
                  <span>Healthcare Standards</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <CheckCircle size={20} className="text-green-500" />
                    <h3 className="font-medium text-green-900">HIPAA Compliance</h3>
                  </div>
                  <p className="text-sm text-green-700">
                    All patient data is encrypted and access is logged. Last audit: Jan 2024
                  </p>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <CheckCircle size={20} className="text-blue-500" />
                    <h3 className="font-medium text-blue-900">HL7 FHIR</h3>
                  </div>
                  <p className="text-sm text-blue-700">
                    Data exchange follows HL7 FHIR standards for interoperability
                  </p>
                </div>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <CheckCircle size={20} className="text-purple-500" />
                    <h3 className="font-medium text-purple-900">GDPR Compliance</h3>
                  </div>
                  <p className="text-sm text-purple-700">
                    Patient consent management and data portability implemented
                  </p>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <AlertCircle size={20} className="text-yellow-500" />
                    <h3 className="font-medium text-yellow-900">NABH Standards</h3>
                  </div>
                  <p className="text-sm text-yellow-700">
                    Quality standards implementation in progress - 85% complete
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}