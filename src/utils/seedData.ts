import User from '../models/User';
import Role from '../models/Role';
import Permission from '../models/Permission';
import bcrypt from 'bcryptjs';

export const seedDatabase = async () => {
  try {
    // Create permissions
    const permissions = [
      // Dashboard permissions
      { module: 'dashboard', action: 'view', resource: '*', description: 'View dashboard' },
      
      // Patient management permissions
      { module: 'patients', action: 'view', resource: '*', description: 'View patients' },
      { module: 'patients', action: 'create', resource: '*', description: 'Create new patients' },
      { module: 'patients', action: 'edit', resource: '*', description: 'Edit patient information' },
      { module: 'patients', action: 'delete', resource: '*', description: 'Delete patients' },
      
      // Clinical management permissions
      { module: 'clinical', action: 'view', resource: '*', description: 'View clinical data' },
      { module: 'clinical', action: 'create', resource: '*', description: 'Create clinical records' },
      { module: 'clinical', action: 'edit', resource: '*', description: 'Edit clinical records' },
      
      // Laboratory permissions
      { module: 'laboratory', action: 'view', resource: '*', description: 'View lab results' },
      { module: 'laboratory', action: 'create', resource: '*', description: 'Create lab orders' },
      { module: 'laboratory', action: 'edit', resource: '*', description: 'Edit lab orders' },
      
      // Pharmacy permissions
      { module: 'pharmacy', action: 'view', resource: '*', description: 'View pharmacy data' },
      { module: 'pharmacy', action: 'create', resource: '*', description: 'Create prescriptions' },
      { module: 'pharmacy', action: 'edit', resource: '*', description: 'Edit prescriptions' },
      
      // Financial permissions
      { module: 'financial', action: 'view', resource: '*', description: 'View financial data' },
      { module: 'financial', action: 'create', resource: '*', description: 'Create invoices' },
      { module: 'financial', action: 'edit', resource: '*', description: 'Edit financial records' },
      
      // HR permissions
      { module: 'hr', action: 'view', resource: '*', description: 'View HR data' },
      { module: 'hr', action: 'create', resource: '*', description: 'Create staff records' },
      { module: 'hr', action: 'edit', resource: '*', description: 'Edit staff records' },
      
      // Facility permissions
      { module: 'facility', action: 'view', resource: '*', description: 'View facility data' },
      { module: 'facility', action: 'create', resource: '*', description: 'Create facility bookings' },
      { module: 'facility', action: 'edit', resource: '*', description: 'Edit facility records' },
      
      // Admin permissions
      { module: 'admin', action: 'view', resource: '*', description: 'View admin panel' },
      { module: 'admin', action: 'view', resource: 'users', description: 'View users' },
      { module: 'admin', action: 'create', resource: 'users', description: 'Create users' },
      { module: 'admin', action: 'edit', resource: 'users', description: 'Edit users' },
      { module: 'admin', action: 'delete', resource: 'users', description: 'Delete users' },
      { module: 'admin', action: 'assign', resource: 'permissions', description: 'Assign permissions' },
      { module: 'admin', action: 'edit', resource: 'settings', description: 'Edit system settings' },
      { module: 'admin', action: 'export', resource: 'logs', description: 'Export audit logs' },
      
      // Reports permissions
      { module: 'reports', action: 'view', resource: '*', description: 'View reports' },
      { module: 'reports', action: 'export', resource: '*', description: 'Export reports' },
    ];

    const createdPermissions = await Permission.insertMany(permissions);
    console.log('Permissions created:', createdPermissions.length);

    // Create roles
    const roles = [
      {
        name: 'Super Admin',
        description: 'Full system access',
        level: 10,
        isSystemRole: true,
        defaultPermissions: createdPermissions.map(p => p._id)
      },
      {
        name: 'Hospital Administrator',
        description: 'Hospital management access',
        level: 9,
        isSystemRole: true,
        defaultPermissions: createdPermissions.filter(p => 
          !p.resource.includes('users') || p.action === 'view'
        ).map(p => p._id)
      },
      {
        name: 'Doctor',
        description: 'Clinical and patient management access',
        level: 7,
        isSystemRole: true,
        defaultPermissions: createdPermissions.filter(p => 
          ['dashboard', 'patients', 'clinical', 'laboratory', 'pharmacy', 'reports'].includes(p.module)
        ).map(p => p._id)
      },
      {
        name: 'Nurse',
        description: 'Patient care and clinical access',
        level: 5,
        isSystemRole: true,
        defaultPermissions: createdPermissions.filter(p => 
          ['dashboard', 'patients', 'clinical', 'pharmacy'].includes(p.module) &&
          p.action !== 'delete'
        ).map(p => p._id)
      },
      {
        name: 'Pharmacist',
        description: 'Pharmacy management access',
        level: 4,
        isSystemRole: true,
        defaultPermissions: createdPermissions.filter(p => 
          ['dashboard', 'pharmacy', 'patients'].includes(p.module) &&
          (p.module !== 'patients' || p.action === 'view')
        ).map(p => p._id)
      },
      {
        name: 'Lab Technician',
        description: 'Laboratory management access',
        level: 4,
        isSystemRole: true,
        defaultPermissions: createdPermissions.filter(p => 
          ['dashboard', 'laboratory', 'patients'].includes(p.module) &&
          (p.module !== 'patients' || p.action === 'view')
        ).map(p => p._id)
      },
      {
        name: 'Receptionist',
        description: 'Patient registration and basic access',
        level: 2,
        isSystemRole: true,
        defaultPermissions: createdPermissions.filter(p => 
          ['dashboard', 'patients'].includes(p.module) &&
          ['view', 'create', 'edit'].includes(p.action)
        ).map(p => p._id)
      }
    ];

    const createdRoles = await Role.insertMany(roles);
    console.log('Roles created:', createdRoles.length);

    // Create default users
    const superAdminRole = createdRoles.find(r => r.name === 'Super Admin');
    const doctorRole = createdRoles.find(r => r.name === 'Doctor');
    const nurseRole = createdRoles.find(r => r.name === 'Nurse');

    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'System',
        lastName: 'Administrator',
        role: superAdminRole?._id,
        permissions: superAdminRole?.defaultPermissions || [],
        department: 'Administration',
        isActive: true
      },
      {
        username: 'doctor1',
        email: '<EMAIL>',
        password: 'doctor123',
        firstName: 'John',
        lastName: 'Smith',
        role: doctorRole?._id,
        permissions: doctorRole?.defaultPermissions || [],
        department: 'Cardiology',
        isActive: true
      },
      {
        username: 'nurse1',
        email: '<EMAIL>',
        password: 'nurse123',
        firstName: 'Sarah',
        lastName: 'Johnson',
        role: nurseRole?._id,
        permissions: nurseRole?.defaultPermissions || [],
        department: 'ICU',
        isActive: true
      }
    ];

    const createdUsers = await User.insertMany(users);
    console.log('Users created:', createdUsers.length);

    console.log('Database seeded successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  }
};