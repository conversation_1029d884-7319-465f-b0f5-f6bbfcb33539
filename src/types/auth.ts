export interface User {
  _id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserR<PERSON>;
  permissions: Permission[];
  department?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserRole {
  _id: string;
  name: string;
  description: string;
  defaultPermissions: Permission[];
  level: number; // 1-10, where 10 is highest (Super Admin)
}

export interface Permission {
  _id: string;
  module: string;
  action: string;
  resource: string;
  description: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
}

export type ModulePermission = {
  module: string;
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canExport: boolean;
  canApprove?: boolean;
  customPermissions?: string[];
};