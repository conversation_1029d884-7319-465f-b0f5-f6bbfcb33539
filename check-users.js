import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/hospital_management';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ Database connection error:', error);
    process.exit(1);
  }
};

const checkUsers = async () => {
  await connectDB();

  try {
    // Define User schema
    const UserSchema = new mongoose.Schema({
      username: { type: String, required: true, unique: true },
      email: { type: String, required: true, unique: true },
      password: { type: String, required: true },
      firstName: { type: String, required: true },
      lastName: { type: String, required: true },
      role: { type: mongoose.Schema.Types.ObjectId, ref: 'Role', required: true },
      permissions: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Permission' }],
      department: String,
      isActive: { type: Boolean, default: true },
      lastLogin: Date,
      resetPasswordToken: String,
      resetPasswordExpire: Date
    }, { timestamps: true });

    const User = mongoose.model('User', UserSchema);
    const users = await User.find({}).select('username email firstName lastName isActive');

    console.log('Users in database:');
    console.log(JSON.stringify(users, null, 2));

    // Check specific admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    console.log('\nAdmin user details:');
    console.log(JSON.stringify(adminUser, null, 2));

  } catch (error) {
    console.error('Error:', error);
  }

  process.exit(0);
};

checkUsers();
