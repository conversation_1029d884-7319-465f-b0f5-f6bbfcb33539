import express from 'express';
import {
  getLabTests,
  getLabTest,
  createLabTest,
  updateLabTest,
  deleteLabTest,
  getPatientLabHistory,
  getLabStats
} from '../controllers/laboratoryController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Lab Tests routes
router.route('/tests')
  .get(getLabTests)
  .post(createLabTest);

router.route('/tests/:id')
  .get(getLabTest)
  .put(updateLabTest)
  .delete(deleteLabTest);

// Patient lab history
router.get('/patients/:patientId/tests', getPatientLabHistory);

// Lab statistics
router.get('/stats', getLabStats);

export default router;
