import express from 'express';
import {
  getStaff,
  getStaffSchedules,
  createStaffSchedule,
  getStaffPerformance,
  createStaffPerformance,
  getHRStats
} from '../controllers/hrController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Staff routes
router.get('/staff', getStaff);

// Schedule routes
router.route('/schedules')
  .get(getStaffSchedules)
  .post(createStaffSchedule);

// Performance routes
router.route('/performance')
  .get(getStaffPerformance)
  .post(createStaffPerformance);

// Statistics
router.get('/stats', getHRStats);

export default router;
