import express from 'express';
import {
  getRooms,
  createRoom,
  getEquipment,
  createEquipment,
  getMaintenanceRequests,
  createMaintenanceRequest,
  getFacilityStats
} from '../controllers/facilityController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Room routes
router.route('/rooms')
  .get(getRooms)
  .post(createRoom);

// Equipment routes
router.route('/equipment')
  .get(getEquipment)
  .post(createEquipment);

// Maintenance routes
router.route('/maintenance')
  .get(getMaintenanceRequests)
  .post(createMaintenanceRequest);

// Statistics
router.get('/stats', getFacilityStats);

export default router;
