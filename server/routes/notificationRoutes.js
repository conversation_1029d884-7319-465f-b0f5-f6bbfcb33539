import express from 'express';
import {
  getNotifications,
  getUnreadCount,
  mark<PERSON><PERSON><PERSON>,
  markAllAsRead,
  createNotification,
  deleteNotification,
  getNotificationPreferences,
  updateNotificationPreferences
} from '../controllers/notificationController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Notification routes
router.route('/')
  .get(getNotifications)
  .post(createNotification);

router.get('/unread-count', getUnreadCount);
router.put('/mark-all-read', markAllAsRead);

router.route('/preferences')
  .get(getNotificationPreferences)
  .put(updateNotificationPreferences);

router.route('/:id')
  .delete(deleteNotification);

router.put('/:id/read', markAsRead);

export default router;
