import express from 'express';
import {
  getMedicalRecords,
  getMedicalRecord,
  createMedicalRecord,
  updateMedicalRecord,
  deleteMedicalRecord,
  getPatientMedicalHistory
} from '../controllers/clinicalController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Medical Records routes
router.route('/medical-records')
  .get(getMedicalRecords)
  .post(createMedicalRecord);

router.route('/medical-records/:id')
  .get(getMedicalRecord)
  .put(updateMedicalRecord)
  .delete(deleteMedicalRecord);

// Patient medical history
router.get('/patients/:patientId/history', getPatientMedicalHistory);

export default router;
