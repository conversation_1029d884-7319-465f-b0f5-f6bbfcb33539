import express from 'express';
import { protect, checkPermission } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Placeholder routes - to be implemented
router.get('/rooms', checkPermission('facility', 'view'), (req, res) => {
  res.json({ success: true, message: 'Facility routes - coming soon' });
});

export default router;
