import express from 'express';
import {
  getAllUsers,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  toggleUserStatus,
  getUserStats,
  getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  getAuditLogs,
  createAuditLog,
  getSystemStats,
  getDepartments,
  getAvailablePermissions,
  getUserPermissions,
  checkUserPermission
} from '../controllers/adminController.js';
import { protect, restrictTo } from '../controllers/authController.js';
import { checkPermission } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// User management routes
router.route('/users')
  .get(checkPermission('admin', 'view', 'users'), getAllUsers)
  .post(checkPermission('admin', 'create', 'users'), createUser);

router.route('/users/:id')
  .put(checkPermission('admin', 'edit', 'users'), updateUser)
  .delete(checkPermission('admin', 'delete', 'users'), deleteUser);

router.put('/users/:id/reset-password', checkPermission('admin', 'edit', 'users'), resetUserPassword);
router.put('/users/:id/toggle-status', checkPermission('admin', 'edit', 'users'), toggleUserStatus);
router.get('/users/stats', checkPermission('admin', 'view', 'users'), getUserStats);

// Role management routes
router.route('/roles')
  .get(checkPermission('admin', 'view', 'roles'), getAllRoles)
  .post(checkPermission('admin', 'create', 'roles'), createRole);

router.route('/roles/:id')
  .put(checkPermission('admin', 'edit', 'roles'), updateRole)
  .delete(checkPermission('admin', 'delete', 'roles'), deleteRole);

// Audit log routes
router.route('/audit-logs')
  .get(checkPermission('admin', 'view', 'system'), getAuditLogs)
  .post(checkPermission('admin', 'edit', 'system'), createAuditLog);

// System statistics
router.get('/system-stats', checkPermission('admin', 'view', 'system'), getSystemStats);

// Get departments list
router.get('/departments', getDepartments);

// ===== PERMISSION MANAGEMENT =====
// Get available permissions
router.get('/permissions', checkPermission('admin', 'view', 'roles'), getAvailablePermissions);

// Get user permissions
router.get('/users/:id/permissions', checkPermission('admin', 'view', 'users'), getUserPermissions);

// Check specific permission for user
router.get('/users/:id/permissions/:permission', checkPermission('admin', 'view', 'users'), checkUserPermission);

export default router;
