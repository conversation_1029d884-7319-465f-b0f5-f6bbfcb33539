import express from 'express';
import {
  getInventoryItems,
  getInventoryItem,
  createInventoryItem,
  updateInventoryItem,
  getPrescriptions,
  createPrescription,
  getPharmacyStats,
  createPharmacyBill,
  getPharmacyBills,
  generateBillPDF,
  createMedicalPrescription,
  getMedicalPrescriptions,
  generatePrescriptionPDF,
  generateInventoryReportPDF,
  getPharmacyAnalytics
} from '../controllers/pharmacyController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Inventory routes
router.route('/inventory')
  .get(getInventoryItems)
  .post(createInventoryItem);

router.route('/inventory/:id')
  .get(getInventoryItem)
  .put(updateInventoryItem);

// Prescription routes
router.route('/prescriptions')
  .get(getPrescriptions)
  .post(createPrescription);

// Statistics and Analytics
router.get('/stats', getPharmacyStats);
router.get('/analytics', getPharmacyAnalytics);

// Billing routes
router.route('/bills')
  .get(getPharmacyBills)
  .post(createPharmacyBill);

router.get('/bills/:id/pdf', generateBillPDF);

// Medical prescription routes
router.route('/medical/prescriptions')
  .get(getMedicalPrescriptions)
  .post(createMedicalPrescription);

router.get('/medical/prescriptions/:id/pdf', generatePrescriptionPDF);

// Reports
router.get('/reports/inventory', generateInventoryReportPDF);

export default router;
