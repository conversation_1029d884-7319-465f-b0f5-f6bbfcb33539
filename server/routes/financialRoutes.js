import express from 'express';
import {
  getBills,
  getBill,
  createBill,
  updateBill,
  addPayment,
  getFinancialStats,
  getPaymentTrends,
  getOverdueBills,
  getRevenueAnalytics,
  getInsuranceClaims,
  generateFinancialReport,
  processPayment,
  getPayments,
  processRefund,
  getPatientPaymentHistory,
  generateBillPDF,
  createPaymentPlan
} from '../controllers/financialController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Bill Management Routes
router.route('/bills')
  .get(getBills)
  .post(createBill);

router.route('/bills/:id')
  .get(getBill)
  .put(updateBill);

// Legacy payment route (for backward compatibility)
router.post('/bills/:id/payments', addPayment);

// Advanced Payment Management Routes
router.route('/payments')
  .get(getPayments)
  .post(processPayment);

router.post('/payments/:id/refund', processRefund);

// Financial Statistics and Analytics
router.get('/stats', getFinancialStats);
router.get('/payment-trends', getPaymentTrends);
router.get('/overdue-bills', getOverdueBills);
router.get('/revenue-analytics', getRevenueAnalytics);
router.get('/insurance-claims', getInsuranceClaims);

// Advanced Reporting
router.post('/reports/generate', generateFinancialReport);

// Patient Payment History
router.get('/patients/:patientId/payments', getPatientPaymentHistory);

// Bill PDF Generation
router.get('/bills/:id/pdf', generateBillPDF);

// Payment Plans
router.post('/bills/:id/payment-plan', createPaymentPlan);

export default router;
