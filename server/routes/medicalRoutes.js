import express from 'express';
import {
  createMedicalPrescription,
  getMedicalPrescriptions,
  generatePrescriptionPDF
} from '../controllers/pharmacyController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Medical prescription routes
router.route('/prescriptions')
  .get(getMedicalPrescriptions)
  .post(createMedicalPrescription);

router.get('/prescriptions/:id/pdf', generatePrescriptionPDF);

export default router;
