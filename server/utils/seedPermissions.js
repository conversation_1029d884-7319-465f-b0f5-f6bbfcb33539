import Permission from '../models/Permission.js';
import Role from '../models/Role.js';
import connectDB from '../config/database.ts';

const permissions = [
  // Dashboard permissions
  { module: 'dashboard', action: 'view', resource: '*', description: 'View dashboard and statistics' },
  
  // Patient permissions
  { module: 'patients', action: 'view', resource: '*', description: 'View patient information' },
  { module: 'patients', action: 'create', resource: '*', description: 'Create new patients' },
  { module: 'patients', action: 'edit', resource: '*', description: 'Edit patient information' },
  { module: 'patients', action: 'delete', resource: '*', description: 'Delete patients' },
  { module: 'patients', action: 'export', resource: '*', description: 'Export patient data' },
  
  // Clinical permissions
  { module: 'clinical', action: 'view', resource: 'appointments', description: 'View appointments' },
  { module: 'clinical', action: 'create', resource: 'appointments', description: 'Create appointments' },
  { module: 'clinical', action: 'edit', resource: 'appointments', description: 'Edit appointments' },
  { module: 'clinical', action: 'delete', resource: 'appointments', description: 'Delete appointments' },
  { module: 'clinical', action: 'view', resource: 'schedules', description: 'View doctor schedules' },
  { module: 'clinical', action: 'view', resource: 'medical-records', description: 'View medical records' },
  { module: 'clinical', action: 'create', resource: 'medical-records', description: 'Create medical records' },
  { module: 'clinical', action: 'edit', resource: 'medical-records', description: 'Edit medical records' },
  
  // Laboratory permissions
  { module: 'laboratory', action: 'view', resource: '*', description: 'View lab tests and results' },
  { module: 'laboratory', action: 'create', resource: '*', description: 'Order lab tests' },
  { module: 'laboratory', action: 'edit', resource: '*', description: 'Update lab test results' },
  { module: 'laboratory', action: 'approve', resource: '*', description: 'Approve lab results' },
  
  // Pharmacy permissions
  { module: 'pharmacy', action: 'view', resource: '*', description: 'View pharmacy inventory and prescriptions' },
  { module: 'pharmacy', action: 'create', resource: '*', description: 'Create prescriptions and manage inventory' },
  { module: 'pharmacy', action: 'edit', resource: '*', description: 'Edit prescriptions and inventory' },
  { module: 'pharmacy', action: 'delete', resource: '*', description: 'Delete prescriptions and inventory items' },
  
  // Financial permissions
  { module: 'financial', action: 'view', resource: '*', description: 'View bills and financial data' },
  { module: 'financial', action: 'create', resource: '*', description: 'Create bills and process payments' },
  { module: 'financial', action: 'edit', resource: '*', description: 'Edit bills and financial records' },
  { module: 'financial', action: 'delete', resource: '*', description: 'Delete financial records' },
  { module: 'financial', action: 'export', resource: '*', description: 'Export financial reports' },
  
  // HR permissions
  { module: 'hr', action: 'view', resource: '*', description: 'View staff information and schedules' },
  { module: 'hr', action: 'create', resource: '*', description: 'Create staff records and schedules' },
  { module: 'hr', action: 'edit', resource: '*', description: 'Edit staff information' },
  { module: 'hr', action: 'delete', resource: '*', description: 'Delete staff records' },
  
  // Facility permissions
  { module: 'facility', action: 'view', resource: '*', description: 'View facility information and equipment' },
  { module: 'facility', action: 'create', resource: '*', description: 'Add facility resources and equipment' },
  { module: 'facility', action: 'edit', resource: '*', description: 'Edit facility information' },
  { module: 'facility', action: 'delete', resource: '*', description: 'Remove facility resources' },
  
  // Admin permissions
  { module: 'admin', action: 'view', resource: 'users', description: 'View user accounts' },
  { module: 'admin', action: 'create', resource: 'users', description: 'Create user accounts' },
  { module: 'admin', action: 'edit', resource: 'users', description: 'Edit user accounts' },
  { module: 'admin', action: 'delete', resource: 'users', description: 'Delete user accounts' },
  { module: 'admin', action: 'view', resource: 'roles', description: 'View roles and permissions' },
  { module: 'admin', action: 'create', resource: 'roles', description: 'Create roles' },
  { module: 'admin', action: 'edit', resource: 'roles', description: 'Edit roles and permissions' },
  { module: 'admin', action: 'delete', resource: 'roles', description: 'Delete roles' },
  { module: 'admin', action: 'view', resource: 'system', description: 'View system settings' },
  { module: 'admin', action: 'edit', resource: 'system', description: 'Edit system settings' },
  
  // Reports permissions
  { module: 'reports', action: 'view', resource: '*', description: 'View all reports' },
  { module: 'reports', action: 'create', resource: 'custom', description: 'Create custom reports' },
  { module: 'reports', action: 'export', resource: '*', description: 'Export reports' },
  { module: 'reports', action: 'view', resource: 'patient-analytics', description: 'View patient analytics' },
  { module: 'reports', action: 'view', resource: 'appointment-analytics', description: 'View appointment analytics' },
  { module: 'reports', action: 'view', resource: 'financial-analytics', description: 'View financial analytics' },
  { module: 'reports', action: 'view', resource: 'lab-analytics', description: 'View laboratory analytics' },
  { module: 'reports', action: 'view', resource: 'operational', description: 'View operational reports' },
  { module: 'reports', action: 'view', resource: 'clinical', description: 'View clinical reports' },
  { module: 'reports', action: 'view', resource: 'quality', description: 'View quality reports' }
];

const rolePermissions = {
  'Administrator': [
    // All permissions
    ...permissions.map(p => `${p.module}:${p.action}:${p.resource}`)
  ],
  'Doctor': [
    'dashboard:view:*',
    'patients:view:*', 'patients:create:*', 'patients:edit:*',
    'clinical:view:appointments', 'clinical:create:appointments', 'clinical:edit:appointments',
    'clinical:view:schedules', 'clinical:view:medical-records', 'clinical:create:medical-records', 'clinical:edit:medical-records',
    'laboratory:view:*', 'laboratory:create:*', 'laboratory:edit:*',
    'pharmacy:view:*', 'pharmacy:create:*',
    'reports:view:*', 'reports:view:patient-analytics', 'reports:view:clinical'
  ],
  'Nurse': [
    'dashboard:view:*',
    'patients:view:*', 'patients:edit:*',
    'clinical:view:appointments', 'clinical:edit:appointments',
    'clinical:view:medical-records', 'clinical:edit:medical-records',
    'laboratory:view:*', 'laboratory:edit:*',
    'pharmacy:view:*'
  ],
  'Receptionist': [
    'dashboard:view:*',
    'patients:view:*', 'patients:create:*', 'patients:edit:*',
    'clinical:view:appointments', 'clinical:create:appointments', 'clinical:edit:appointments',
    'clinical:view:schedules',
    'financial:view:*', 'financial:create:*', 'financial:edit:*'
  ]
};

export const seedPermissions = async () => {
  try {
    console.log('🔐 Starting permissions seeding...');
    
    await connectDB();
    
    // Clear existing permissions
    await Permission.deleteMany({});
    console.log('🗑️ Cleared existing permissions');
    
    // Create permissions
    const createdPermissions = await Permission.insertMany(permissions);
    console.log(`✅ Created ${createdPermissions.length} permissions`);
    
    // Update roles with permissions
    for (const [roleName, permissionKeys] of Object.entries(rolePermissions)) {
      const role = await Role.findOne({ name: roleName });
      if (role) {
        const rolePermissionIds = [];
        
        for (const permKey of permissionKeys) {
          const [module, action, resource] = permKey.split(':');
          const permission = await Permission.findOne({ module, action, resource });
          if (permission) {
            rolePermissionIds.push(permission._id);
          }
        }
        
        role.defaultPermissions = rolePermissionIds;
        await role.save();
        console.log(`✅ Updated ${roleName} role with ${rolePermissionIds.length} permissions`);
      }
    }
    
    console.log('🎉 Permissions seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding permissions:', error);
    throw error;
  }
};

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedPermissions()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}
