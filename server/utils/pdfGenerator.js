import PDFDocument from 'pdfkit';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Hospital Configuration
const HOSPITAL_CONFIG = {
  name: 'HOSPITAL MANAGEMENT SYSTEM',
  address: '123 Medical Center Drive',
  city: 'Healthcare City, HC 12345',
  phone: '+****************',
  email: '<EMAIL>',
  website: 'www.hospital.com',
  taxId: 'TAX123456789',
  logo: null // Path to logo file if available
};

// Professional PDF Bill Generator
export class ProfessionalBillGenerator {
  constructor() {
    this.doc = null;
    this.currentY = 0;
    this.pageMargin = 50;
    this.pageWidth = 595.28; // A4 width in points
    this.pageHeight = 841.89; // A4 height in points
  }

  // Generate comprehensive bill PDF
  async generateBill(billData, billType = 'Patient Bill') {
    return new Promise((resolve, reject) => {
      try {
        this.doc = new PDFDocument({ 
          margin: this.pageMargin,
          size: 'A4'
        });
        
        const chunks = [];
        this.doc.on('data', chunk => chunks.push(chunk));
        this.doc.on('end', () => resolve(Buffer.concat(chunks)));
        this.doc.on('error', reject);

        this.currentY = this.pageMargin;

        // Generate bill based on type
        switch (billType) {
          case 'Patient Bill':
          case 'Emergency Bill':
          case 'Insurance Bill':
            this.generatePatientBill(billData);
            break;
          case 'Vendor Bill':
          case 'Supplier Bill':
            this.generateVendorBill(billData);
            break;
          case 'Employee Expense':
          case 'Payroll':
            this.generateEmployeeBill(billData);
            break;
          default:
            this.generateGenericBill(billData);
        }

        this.doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  // Generate patient bill
  generatePatientBill(bill) {
    this.addHeader();
    this.addBillTitle('MEDICAL BILL INVOICE', bill.billNumber);
    this.addPatientInfo(bill);
    this.addBillDetails(bill);
    this.addItemsTable(bill.items);
    this.addTotalsSection(bill);
    this.addPaymentInfo(bill);
    this.addFooter();
  }

  // Generate vendor bill
  generateVendorBill(bill) {
    this.addHeader();
    this.addBillTitle('VENDOR INVOICE', bill.billNumber);
    this.addVendorInfo(bill);
    this.addBillDetails(bill);
    this.addItemsTable(bill.items);
    this.addTotalsSection(bill);
    this.addPaymentInfo(bill);
    this.addFooter();
  }

  // Generate employee bill
  generateEmployeeBill(bill) {
    this.addHeader();
    this.addBillTitle('EMPLOYEE EXPENSE REPORT', bill.billNumber);
    this.addEmployeeInfo(bill);
    this.addBillDetails(bill);
    this.addItemsTable(bill.items);
    this.addTotalsSection(bill);
    this.addPaymentInfo(bill);
    this.addFooter();
  }

  // Generate generic bill
  generateGenericBill(bill) {
    this.addHeader();
    this.addBillTitle('INVOICE', bill.billNumber);
    this.addGenericInfo(bill);
    this.addBillDetails(bill);
    this.addItemsTable(bill.items);
    this.addTotalsSection(bill);
    this.addPaymentInfo(bill);
    this.addFooter();
  }

  // Add hospital header
  addHeader() {
    const headerHeight = 80;
    
    // Hospital name
    this.doc.fontSize(24)
      .font('Helvetica-Bold')
      .fillColor('#2563eb')
      .text(HOSPITAL_CONFIG.name, this.pageMargin, this.currentY, {
        width: this.pageWidth - 2 * this.pageMargin,
        align: 'center'
      });

    this.currentY += 30;

    // Hospital details
    this.doc.fontSize(10)
      .font('Helvetica')
      .fillColor('#666666')
      .text(HOSPITAL_CONFIG.address, this.pageMargin, this.currentY, {
        width: this.pageWidth - 2 * this.pageMargin,
        align: 'center'
      });

    this.currentY += 12;

    this.doc.text(`${HOSPITAL_CONFIG.city} | Phone: ${HOSPITAL_CONFIG.phone} | Email: ${HOSPITAL_CONFIG.email}`, {
      width: this.pageWidth - 2 * this.pageMargin,
      align: 'center'
    });

    this.currentY += 20;

    // Add horizontal line
    this.doc.strokeColor('#e5e7eb')
      .lineWidth(1)
      .moveTo(this.pageMargin, this.currentY)
      .lineTo(this.pageWidth - this.pageMargin, this.currentY)
      .stroke();

    this.currentY += 20;
  }

  // Add bill title and number
  addBillTitle(title, billNumber) {
    const titleY = this.currentY;
    
    // Title
    this.doc.fontSize(18)
      .font('Helvetica-Bold')
      .fillColor('#1f2937')
      .text(title, this.pageMargin, titleY);

    // Bill number and date (right aligned)
    const rightX = this.pageWidth - this.pageMargin - 150;
    this.doc.fontSize(12)
      .font('Helvetica-Bold')
      .text(`Bill #: ${billNumber}`, rightX, titleY);

    this.doc.fontSize(10)
      .font('Helvetica')
      .fillColor('#666666')
      .text(`Date: ${new Date().toLocaleDateString()}`, rightX, titleY + 20);

    this.currentY += 50;
  }

  // Add patient information
  addPatientInfo(bill) {
    if (!bill.patient) return;

    this.doc.fontSize(14)
      .font('Helvetica-Bold')
      .fillColor('#1f2937')
      .text('PATIENT INFORMATION', this.pageMargin, this.currentY);

    this.currentY += 20;

    const patient = bill.patient;
    const infoLines = [
      `Name: ${patient.firstName} ${patient.lastName}`,
      `Patient ID: ${patient.patientId}`,
      `Phone: ${patient.phone || 'N/A'}`,
      `Email: ${patient.email || 'N/A'}`
    ];

    this.doc.fontSize(10)
      .font('Helvetica')
      .fillColor('#374151');

    infoLines.forEach(line => {
      this.doc.text(line, this.pageMargin, this.currentY);
      this.currentY += 15;
    });

    this.currentY += 10;
  }

  // Add vendor information
  addVendorInfo(bill) {
    if (!bill.vendor) return;

    this.doc.fontSize(14)
      .font('Helvetica-Bold')
      .fillColor('#1f2937')
      .text('VENDOR INFORMATION', this.pageMargin, this.currentY);

    this.currentY += 20;

    const vendor = bill.vendor;
    const infoLines = [
      `Vendor: ${vendor.name}`,
      `Contact: ${vendor.contactPerson || 'N/A'}`,
      `Phone: ${vendor.phone || 'N/A'}`,
      `Email: ${vendor.email || 'N/A'}`
    ];

    this.doc.fontSize(10)
      .font('Helvetica')
      .fillColor('#374151');

    infoLines.forEach(line => {
      this.doc.text(line, this.pageMargin, this.currentY);
      this.currentY += 15;
    });

    this.currentY += 10;
  }

  // Add employee information
  addEmployeeInfo(bill) {
    if (!bill.employee) return;

    this.doc.fontSize(14)
      .font('Helvetica-Bold')
      .fillColor('#1f2937')
      .text('EMPLOYEE INFORMATION', this.pageMargin, this.currentY);

    this.currentY += 20;

    const employee = bill.employee;
    const infoLines = [
      `Employee: ${employee.firstName} ${employee.lastName}`,
      `Employee ID: ${employee.employeeId || employee._id}`,
      `Department: ${employee.department || 'N/A'}`,
      `Email: ${employee.email || 'N/A'}`
    ];

    this.doc.fontSize(10)
      .font('Helvetica')
      .fillColor('#374151');

    infoLines.forEach(line => {
      this.doc.text(line, this.pageMargin, this.currentY);
      this.currentY += 15;
    });

    this.currentY += 10;
  }

  // Add generic information
  addGenericInfo(bill) {
    this.doc.fontSize(14)
      .font('Helvetica-Bold')
      .fillColor('#1f2937')
      .text('BILL INFORMATION', this.pageMargin, this.currentY);

    this.currentY += 20;

    const infoLines = [
      `Bill Type: ${bill.billType}`,
      `Category: ${bill.category}`,
      `Department: ${bill.department || 'N/A'}`
    ];

    this.doc.fontSize(10)
      .font('Helvetica')
      .fillColor('#374151');

    infoLines.forEach(line => {
      this.doc.text(line, this.pageMargin, this.currentY);
      this.currentY += 15;
    });

    this.currentY += 10;
  }

  // Add bill details
  addBillDetails(bill) {
    const detailsY = this.currentY;
    const rightX = this.pageWidth - this.pageMargin - 200;

    // Left side - Bill details
    this.doc.fontSize(12)
      .font('Helvetica-Bold')
      .fillColor('#1f2937')
      .text('BILL DETAILS', this.pageMargin, detailsY);

    this.currentY = detailsY + 20;

    const leftDetails = [
      `Bill Date: ${new Date(bill.billDate).toLocaleDateString()}`,
      `Due Date: ${new Date(bill.dueDate).toLocaleDateString()}`,
      `Payment Status: ${bill.paymentStatus}`
    ];

    this.doc.fontSize(10)
      .font('Helvetica')
      .fillColor('#374151');

    leftDetails.forEach(detail => {
      this.doc.text(detail, this.pageMargin, this.currentY);
      this.currentY += 15;
    });

    // Right side - Additional details
    let rightY = detailsY + 20;
    
    if (bill.poNumber) {
      this.doc.text(`PO Number: ${bill.poNumber}`, rightX, rightY);
      rightY += 15;
    }
    
    if (bill.invoiceNumber) {
      this.doc.text(`Invoice #: ${bill.invoiceNumber}`, rightX, rightY);
      rightY += 15;
    }
    
    if (bill.costCenter) {
      this.doc.text(`Cost Center: ${bill.costCenter}`, rightX, rightY);
      rightY += 15;
    }

    this.currentY = Math.max(this.currentY, rightY) + 20;
  }

  // Add items table
  addItemsTable(items) {
    if (!items || items.length === 0) return;

    // Table header
    this.doc.fontSize(12)
      .font('Helvetica-Bold')
      .fillColor('#1f2937')
      .text('SERVICES & ITEMS', this.pageMargin, this.currentY);

    this.currentY += 25;

    // Table structure
    const tableTop = this.currentY;
    const tableLeft = this.pageMargin;
    const tableWidth = this.pageWidth - 2 * this.pageMargin;

    const colWidths = {
      description: tableWidth * 0.4,
      quantity: tableWidth * 0.1,
      unitPrice: tableWidth * 0.15,
      discount: tableWidth * 0.15,
      total: tableWidth * 0.2
    };

    let currentX = tableLeft;

    // Header background
    this.doc.rect(tableLeft, tableTop, tableWidth, 25)
      .fillColor('#f3f4f6')
      .fill();

    // Header text
    this.doc.fontSize(10)
      .font('Helvetica-Bold')
      .fillColor('#374151');

    this.doc.text('Description', currentX + 5, tableTop + 8);
    currentX += colWidths.description;

    this.doc.text('Qty', currentX + 5, tableTop + 8);
    currentX += colWidths.quantity;

    this.doc.text('Unit Price', currentX + 5, tableTop + 8);
    currentX += colWidths.unitPrice;

    this.doc.text('Discount', currentX + 5, tableTop + 8);
    currentX += colWidths.discount;

    this.doc.text('Total', currentX + 5, tableTop + 8);

    this.currentY = tableTop + 25;

    // Table rows
    this.doc.font('Helvetica')
      .fillColor('#374151');

    items.forEach((item, index) => {
      const rowHeight = 20;
      const rowY = this.currentY;

      // Alternate row background
      if (index % 2 === 1) {
        this.doc.rect(tableLeft, rowY, tableWidth, rowHeight)
          .fillColor('#f9fafb')
          .fill();
      }

      currentX = tableLeft;

      // Item data
      this.doc.fillColor('#374151')
        .text(item.description || '', currentX + 5, rowY + 5, {
          width: colWidths.description - 10,
          ellipsis: true
        });
      currentX += colWidths.description;

      this.doc.text(item.quantity?.toString() || '0', currentX + 5, rowY + 5);
      currentX += colWidths.quantity;

      this.doc.text(`$${(item.unitPrice || 0).toFixed(2)}`, currentX + 5, rowY + 5);
      currentX += colWidths.unitPrice;

      this.doc.text(`$${(item.discount || 0).toFixed(2)}`, currentX + 5, rowY + 5);
      currentX += colWidths.discount;

      this.doc.text(`$${(item.totalPrice || 0).toFixed(2)}`, currentX + 5, rowY + 5);

      this.currentY += rowHeight;
    });

    // Table border
    this.doc.strokeColor('#e5e7eb')
      .lineWidth(1)
      .rect(tableLeft, tableTop, tableWidth, this.currentY - tableTop)
      .stroke();

    this.currentY += 20;
  }

  // Add totals section
  addTotalsSection(bill) {
    const totalsX = this.pageWidth - this.pageMargin - 200;
    const totalsWidth = 200;

    // Totals background
    this.doc.rect(totalsX, this.currentY, totalsWidth, 120)
      .fillColor('#f8fafc')
      .fill()
      .strokeColor('#e5e7eb')
      .stroke();

    this.currentY += 10;

    this.doc.fontSize(10)
      .font('Helvetica')
      .fillColor('#374151');

    // Subtotal
    this.doc.text('Subtotal:', totalsX + 10, this.currentY);
    this.doc.text(`$${(bill.subtotal || 0).toFixed(2)}`, totalsX + 120, this.currentY, {
      width: 70,
      align: 'right'
    });
    this.currentY += 15;

    // Tax
    this.doc.text('Tax:', totalsX + 10, this.currentY);
    this.doc.text(`$${(bill.taxAmount || 0).toFixed(2)}`, totalsX + 120, this.currentY, {
      width: 70,
      align: 'right'
    });
    this.currentY += 15;

    // Discount
    this.doc.text('Discount:', totalsX + 10, this.currentY);
    this.doc.text(`-$${(bill.discountAmount || 0).toFixed(2)}`, totalsX + 120, this.currentY, {
      width: 70,
      align: 'right'
    });
    this.currentY += 15;

    // Total line
    this.doc.strokeColor('#d1d5db')
      .lineWidth(1)
      .moveTo(totalsX + 10, this.currentY)
      .lineTo(totalsX + 190, this.currentY)
      .stroke();

    this.currentY += 10;

    // Total amount
    this.doc.fontSize(12)
      .font('Helvetica-Bold')
      .fillColor('#1f2937')
      .text('Total Amount:', totalsX + 10, this.currentY);
    this.doc.text(`$${(bill.totalAmount || 0).toFixed(2)}`, totalsX + 120, this.currentY, {
      width: 70,
      align: 'right'
    });
    this.currentY += 20;

    // Paid amount
    this.doc.fontSize(10)
      .font('Helvetica')
      .fillColor('#059669')
      .text('Paid Amount:', totalsX + 10, this.currentY);
    this.doc.text(`$${(bill.paidAmount || 0).toFixed(2)}`, totalsX + 120, this.currentY, {
      width: 70,
      align: 'right'
    });
    this.currentY += 15;

    // Balance
    this.doc.fillColor('#dc2626')
      .text('Balance Due:', totalsX + 10, this.currentY);
    this.doc.text(`$${(bill.balanceAmount || 0).toFixed(2)}`, totalsX + 120, this.currentY, {
      width: 70,
      align: 'right'
    });

    this.currentY += 40;
  }

  // Add payment information
  addPaymentInfo(bill) {
    if (bill.payments && bill.payments.length > 0) {
      this.doc.fontSize(12)
        .font('Helvetica-Bold')
        .fillColor('#1f2937')
        .text('PAYMENT HISTORY', this.pageMargin, this.currentY);

      this.currentY += 20;

      this.doc.fontSize(10)
        .font('Helvetica')
        .fillColor('#374151');

      bill.payments.forEach(payment => {
        this.doc.text(
          `${new Date(payment.paymentDate).toLocaleDateString()} - ${payment.paymentMethod} - $${payment.amount.toFixed(2)}`,
          this.pageMargin,
          this.currentY
        );
        this.currentY += 15;
      });

      this.currentY += 10;
    }

    // Notes
    if (bill.notes) {
      this.doc.fontSize(12)
        .font('Helvetica-Bold')
        .fillColor('#1f2937')
        .text('NOTES', this.pageMargin, this.currentY);

      this.currentY += 20;

      this.doc.fontSize(10)
        .font('Helvetica')
        .fillColor('#374151')
        .text(bill.notes, this.pageMargin, this.currentY, {
          width: this.pageWidth - 2 * this.pageMargin
        });

      this.currentY += 30;
    }
  }

  // Add footer
  addFooter() {
    const footerY = this.pageHeight - 100;

    // Footer line
    this.doc.strokeColor('#e5e7eb')
      .lineWidth(1)
      .moveTo(this.pageMargin, footerY)
      .lineTo(this.pageWidth - this.pageMargin, footerY)
      .stroke();

    // Footer text
    this.doc.fontSize(8)
      .font('Helvetica')
      .fillColor('#6b7280')
      .text('Thank you for choosing our hospital services!', this.pageMargin, footerY + 10);

    this.doc.text(
      `Generated on ${new Date().toLocaleString()} | Tax ID: ${HOSPITAL_CONFIG.taxId}`,
      this.pageMargin,
      footerY + 25
    );

    this.doc.text(
      'This is a computer-generated document and does not require a signature.',
      this.pageMargin,
      footerY + 40,
      {
        width: this.pageWidth - 2 * this.pageMargin,
        align: 'center'
      }
    );
  }
}

// Export default instance
export default new ProfessionalBillGenerator();
