import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Simple schemas for seeding
const PermissionSchema = new mongoose.Schema({
  module: { type: String, required: true },
  action: { type: String, required: true },
  resource: { type: String, required: true },
  description: { type: String, required: true },
  isSystemPermission: { type: Boolean, default: false }
}, { timestamps: true });

const RoleSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true },
  description: { type: String, required: true },
  defaultPermissions: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Permission' }],
  level: { type: Number, required: true, min: 1, max: 10 },
  isSystemRole: { type: Boolean, default: false }
}, { timestamps: true });

const UserSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  role: { type: mongoose.Schema.Types.ObjectId, ref: 'Role', required: true },
  permissions: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Permission' }],
  department: String,
  isActive: { type: Boolean, default: true },
  lastLogin: Date,
  resetPasswordToken: String,
  resetPasswordExpire: Date
}, { timestamps: true });

// Hash password before saving
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

const Permission = mongoose.model('Permission', PermissionSchema);
const Role = mongoose.model('Role', RoleSchema);
const User = mongoose.model('User', UserSchema);

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/hospital_management';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ Database connection error:', error);
    process.exit(1);
  }
};

export const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Connect to database
    await connectDB();

    // Clear existing data
    await Promise.all([
      Permission.deleteMany({}),
      Role.deleteMany({}),
      User.deleteMany({})
    ]);

    console.log('🗑️ Cleared existing data');

    // Create permissions
    const permissions = [
      // Dashboard permissions
      { module: 'dashboard', action: 'view', resource: '*', description: 'View dashboard', isSystemPermission: true },

      // Patient management permissions
      { module: 'patients', action: 'view', resource: '*', description: 'View patients', isSystemPermission: true },
      { module: 'patients', action: 'create', resource: '*', description: 'Create new patients', isSystemPermission: true },
      { module: 'patients', action: 'edit', resource: '*', description: 'Edit patient information', isSystemPermission: true },
      { module: 'patients', action: 'delete', resource: '*', description: 'Delete patients', isSystemPermission: true },
      { module: 'patients', action: 'view', resource: 'own', description: 'View own assigned patients', isSystemPermission: true },

      // Appointment management permissions
      { module: 'appointments', action: 'view', resource: '*', description: 'View all appointments', isSystemPermission: true },
      { module: 'appointments', action: 'create', resource: '*', description: 'Create appointments', isSystemPermission: true },
      { module: 'appointments', action: 'edit', resource: '*', description: 'Edit appointments', isSystemPermission: true },
      { module: 'appointments', action: 'delete', resource: '*', description: 'Cancel appointments', isSystemPermission: true },
      { module: 'appointments', action: 'view', resource: 'own', description: 'View own appointments', isSystemPermission: true },

      // Clinical permissions
      { module: 'clinical', action: 'view', resource: '*', description: 'View clinical records', isSystemPermission: true },
      { module: 'clinical', action: 'create', resource: '*', description: 'Create clinical records', isSystemPermission: true },
      { module: 'clinical', action: 'edit', resource: '*', description: 'Edit clinical records', isSystemPermission: true },
      { module: 'clinical', action: 'view', resource: 'own', description: 'View own clinical records', isSystemPermission: true },

      // Laboratory permissions
      { module: 'laboratory', action: 'view', resource: '*', description: 'View lab tests', isSystemPermission: true },
      { module: 'laboratory', action: 'create', resource: '*', description: 'Create lab orders', isSystemPermission: true },
      { module: 'laboratory', action: 'edit', resource: '*', description: 'Edit lab results', isSystemPermission: true },
      { module: 'laboratory', action: 'process', resource: '*', description: 'Process lab tests', isSystemPermission: true },

      // Pharmacy permissions
      { module: 'pharmacy', action: 'view', resource: '*', description: 'View pharmacy inventory', isSystemPermission: true },
      { module: 'pharmacy', action: 'create', resource: '*', description: 'Create prescriptions', isSystemPermission: true },
      { module: 'pharmacy', action: 'edit', resource: '*', description: 'Edit inventory', isSystemPermission: true },
      { module: 'pharmacy', action: 'dispense', resource: '*', description: 'Dispense medications', isSystemPermission: true },

      // Financial permissions
      { module: 'financial', action: 'view', resource: '*', description: 'View financial records', isSystemPermission: true },
      { module: 'financial', action: 'create', resource: '*', description: 'Create bills', isSystemPermission: true },
      { module: 'financial', action: 'edit', resource: '*', description: 'Edit financial records', isSystemPermission: true },
      { module: 'financial', action: 'process', resource: 'payments', description: 'Process payments', isSystemPermission: true },

      // HR permissions
      { module: 'hr', action: 'view', resource: '*', description: 'View HR records', isSystemPermission: true },
      { module: 'hr', action: 'create', resource: '*', description: 'Create staff records', isSystemPermission: true },
      { module: 'hr', action: 'edit', resource: '*', description: 'Edit staff records', isSystemPermission: true },
      { module: 'hr', action: 'view', resource: 'schedules', description: 'View staff schedules', isSystemPermission: true },

      // Facility permissions
      { module: 'facility', action: 'view', resource: '*', description: 'View facility management', isSystemPermission: true },
      { module: 'facility', action: 'edit', resource: '*', description: 'Edit facility records', isSystemPermission: true },
      { module: 'facility', action: 'view', resource: 'rooms', description: 'View room status', isSystemPermission: true },

      // Admin permissions
      { module: 'admin', action: 'view', resource: 'users', description: 'View users', isSystemPermission: true },
      { module: 'admin', action: 'create', resource: 'users', description: 'Create users', isSystemPermission: true },
      { module: 'admin', action: 'edit', resource: 'users', description: 'Edit users', isSystemPermission: true },
      { module: 'admin', action: 'delete', resource: 'users', description: 'Delete users', isSystemPermission: true },
      { module: 'admin', action: 'view', resource: 'roles', description: 'View roles and permissions', isSystemPermission: true },
      { module: 'admin', action: 'edit', resource: 'permissions', description: 'Manage permissions', isSystemPermission: true },

      // Reports permissions
      { module: 'reports', action: 'view', resource: '*', description: 'View reports', isSystemPermission: true },
      { module: 'reports', action: 'export', resource: '*', description: 'Export reports', isSystemPermission: true },
      { module: 'reports', action: 'view', resource: 'basic', description: 'View basic reports', isSystemPermission: true }
    ];

    const createdPermissions = await Permission.insertMany(permissions);
    console.log(`✅ Created ${createdPermissions.length} permissions`);

    // Create roles with permissions
    const allPermissionIds = createdPermissions.map(p => p._id);

    const roles = [
      {
        name: 'Super Admin',
        description: 'Full system access with all permissions',
        level: 10,
        isSystemRole: true,
        defaultPermissions: allPermissionIds
      },
      {
        name: 'Doctor',
        description: 'Medical staff with clinical and patient management access',
        level: 8,
        isSystemRole: true,
        defaultPermissions: allPermissionIds.filter(id => {
          const permission = createdPermissions.find(p => p._id.equals(id));
          return permission && (
            ['dashboard', 'patients', 'appointments', 'clinical', 'laboratory', 'pharmacy'].includes(permission.module) ||
            (permission.module === 'reports' && permission.resource === 'basic')
          );
        })
      },
      {
        name: 'Nurse',
        description: 'Nursing staff with patient care and clinical access',
        level: 6,
        isSystemRole: true,
        defaultPermissions: allPermissionIds.filter(id => {
          const permission = createdPermissions.find(p => p._id.equals(id));
          return permission && (
            (['dashboard', 'patients', 'clinical', 'laboratory'].includes(permission.module) && permission.action !== 'delete') ||
            (permission.module === 'appointments' && ['view', 'edit'].includes(permission.action)) ||
            (permission.module === 'pharmacy' && permission.action === 'view') ||
            (permission.module === 'facility' && permission.resource === 'rooms')
          );
        })
      },
      {
        name: 'Receptionist',
        description: 'Front desk staff with appointment and patient registration access',
        level: 4,
        isSystemRole: true,
        defaultPermissions: allPermissionIds.filter(id => {
          const permission = createdPermissions.find(p => p._id.equals(id));
          return permission && (
            (permission.module === 'dashboard' && permission.action === 'view') ||
            (['patients', 'appointments'].includes(permission.module) && ['view', 'create', 'edit'].includes(permission.action)) ||
            (permission.module === 'financial' && ['view', 'create', 'process'].includes(permission.action)) ||
            (permission.module === 'facility' && permission.resource === 'rooms') ||
            (permission.module === 'reports' && permission.resource === 'basic')
          );
        })
      }
    ];

    const createdRoles = await Role.insertMany(roles);
    console.log(`✅ Created ${createdRoles.length} roles`);

    // Create default users for each role
    const superAdminRole = createdRoles.find(r => r.name === 'Super Admin');
    const doctorRole = createdRoles.find(r => r.name === 'Doctor');
    const nurseRole = createdRoles.find(r => r.name === 'Nurse');
    const receptionistRole = createdRoles.find(r => r.name === 'Receptionist');

    // Hash passwords before creating users
    const salt = await bcrypt.genSalt(12);
    const hashedAdminPassword = await bcrypt.hash('admin123', salt);
    const hashedDoctorPassword = await bcrypt.hash('doctor123', salt);
    const hashedNursePassword = await bcrypt.hash('nurse123', salt);
    const hashedReceptionistPassword = await bcrypt.hash('reception123', salt);

    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: hashedAdminPassword,
        firstName: 'System',
        lastName: 'Administrator',
        role: superAdminRole._id,
        permissions: superAdminRole.defaultPermissions,
        department: 'Administration',
        isActive: true
      },
      {
        username: 'dr.smith',
        email: '<EMAIL>',
        password: hashedDoctorPassword,
        firstName: 'John',
        lastName: 'Smith',
        role: doctorRole._id,
        permissions: doctorRole.defaultPermissions,
        department: 'Cardiology',
        isActive: true
      },
      {
        username: 'nurse.johnson',
        email: '<EMAIL>',
        password: hashedNursePassword,
        firstName: 'Sarah',
        lastName: 'Johnson',
        role: nurseRole._id,
        permissions: nurseRole.defaultPermissions,
        department: 'General Medicine',
        isActive: true
      },
      {
        username: 'reception.mary',
        email: '<EMAIL>',
        password: hashedReceptionistPassword,
        firstName: 'Mary',
        lastName: 'Williams',
        role: receptionistRole._id,
        permissions: receptionistRole.defaultPermissions,
        department: 'Front Desk',
        isActive: true
      }
    ];

    const createdUsers = await User.insertMany(users);
    console.log(`✅ Created ${createdUsers.length} users`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Default Login Credentials:');
    console.log('Super Admin: <EMAIL> / admin123');
    console.log('Doctor: <EMAIL> / doctor123');
    console.log('Nurse: <EMAIL> / nurse123');
    console.log('Receptionist: <EMAIL> / reception123');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
};

// Run seeding if this file is executed directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}
