import User from '../models/User.js';
import Role from '../models/Role.js';
import Permission from '../models/Permission.js';
import Patient from '../models/Patient.js';
import connectDB from '../config/database.js';

export const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Connect to database
    await connectDB();

    // Clear existing data
    await Promise.all([
      Permission.deleteMany({}),
      Role.deleteMany({}),
      User.deleteMany({}),
      Patient.deleteMany({})
    ]);

    console.log('🗑️ Cleared existing data');

    // Create comprehensive permissions
    const permissions = [
      // Dashboard permissions
      { module: 'dashboard', action: 'view', resource: '*', description: 'View dashboard and statistics', isSystemPermission: true },

      // Patient permissions
      { module: 'patients', action: 'view', resource: '*', description: 'View patient information', isSystemPermission: true },
      { module: 'patients', action: 'create', resource: '*', description: 'Create new patients', isSystemPermission: true },
      { module: 'patients', action: 'edit', resource: '*', description: 'Edit patient information', isSystemPermission: true },
      { module: 'patients', action: 'delete', resource: '*', description: 'Delete patients', isSystemPermission: true },
      { module: 'patients', action: 'export', resource: '*', description: 'Export patient data', isSystemPermission: true },

      // Clinical permissions
      { module: 'clinical', action: 'view', resource: 'appointments', description: 'View appointments', isSystemPermission: true },
      { module: 'clinical', action: 'create', resource: 'appointments', description: 'Create appointments', isSystemPermission: true },
      { module: 'clinical', action: 'edit', resource: 'appointments', description: 'Edit appointments', isSystemPermission: true },
      { module: 'clinical', action: 'delete', resource: 'appointments', description: 'Delete appointments', isSystemPermission: true },
      { module: 'clinical', action: 'view', resource: 'schedules', description: 'View doctor schedules', isSystemPermission: true },
      { module: 'clinical', action: 'view', resource: 'medical-records', description: 'View medical records', isSystemPermission: true },
      { module: 'clinical', action: 'create', resource: 'medical-records', description: 'Create medical records', isSystemPermission: true },
      { module: 'clinical', action: 'edit', resource: 'medical-records', description: 'Edit medical records', isSystemPermission: true },

      // Laboratory permissions
      { module: 'laboratory', action: 'view', resource: '*', description: 'View lab tests and results', isSystemPermission: true },
      { module: 'laboratory', action: 'create', resource: '*', description: 'Order lab tests', isSystemPermission: true },
      { module: 'laboratory', action: 'edit', resource: '*', description: 'Update lab test results', isSystemPermission: true },
      { module: 'laboratory', action: 'approve', resource: '*', description: 'Approve lab results', isSystemPermission: true },

      // Pharmacy permissions
      { module: 'pharmacy', action: 'view', resource: '*', description: 'View pharmacy inventory and prescriptions', isSystemPermission: true },
      { module: 'pharmacy', action: 'create', resource: '*', description: 'Create prescriptions and manage inventory', isSystemPermission: true },
      { module: 'pharmacy', action: 'edit', resource: '*', description: 'Edit prescriptions and inventory', isSystemPermission: true },
      { module: 'pharmacy', action: 'delete', resource: '*', description: 'Delete prescriptions and inventory items', isSystemPermission: true },

      // Financial permissions
      { module: 'financial', action: 'view', resource: '*', description: 'View bills and financial data', isSystemPermission: true },
      { module: 'financial', action: 'create', resource: '*', description: 'Create bills and process payments', isSystemPermission: true },
      { module: 'financial', action: 'edit', resource: '*', description: 'Edit bills and financial records', isSystemPermission: true },
      { module: 'financial', action: 'delete', resource: '*', description: 'Delete financial records', isSystemPermission: true },
      { module: 'financial', action: 'export', resource: '*', description: 'Export financial reports', isSystemPermission: true },

      // HR permissions
      { module: 'hr', action: 'view', resource: '*', description: 'View staff information and schedules', isSystemPermission: true },
      { module: 'hr', action: 'create', resource: '*', description: 'Create staff records and schedules', isSystemPermission: true },
      { module: 'hr', action: 'edit', resource: '*', description: 'Edit staff information', isSystemPermission: true },
      { module: 'hr', action: 'delete', resource: '*', description: 'Delete staff records', isSystemPermission: true },

      // Facility permissions
      { module: 'facility', action: 'view', resource: '*', description: 'View facility information and equipment', isSystemPermission: true },
      { module: 'facility', action: 'create', resource: '*', description: 'Add facility resources and equipment', isSystemPermission: true },
      { module: 'facility', action: 'edit', resource: '*', description: 'Edit facility information', isSystemPermission: true },
      { module: 'facility', action: 'delete', resource: '*', description: 'Remove facility resources', isSystemPermission: true },

      // Admin permissions
      { module: 'admin', action: 'view', resource: 'users', description: 'View user accounts', isSystemPermission: true },
      { module: 'admin', action: 'create', resource: 'users', description: 'Create user accounts', isSystemPermission: true },
      { module: 'admin', action: 'edit', resource: 'users', description: 'Edit user accounts', isSystemPermission: true },
      { module: 'admin', action: 'delete', resource: 'users', description: 'Delete user accounts', isSystemPermission: true },
      { module: 'admin', action: 'view', resource: 'roles', description: 'View roles and permissions', isSystemPermission: true },
      { module: 'admin', action: 'create', resource: 'roles', description: 'Create roles', isSystemPermission: true },
      { module: 'admin', action: 'edit', resource: 'roles', description: 'Edit roles and permissions', isSystemPermission: true },
      { module: 'admin', action: 'delete', resource: 'roles', description: 'Delete roles', isSystemPermission: true },
      { module: 'admin', action: 'view', resource: 'system', description: 'View system settings', isSystemPermission: true },
      { module: 'admin', action: 'edit', resource: 'system', description: 'Edit system settings', isSystemPermission: true },

      // Reports permissions
      { module: 'reports', action: 'view', resource: '*', description: 'View all reports', isSystemPermission: true },
      { module: 'reports', action: 'create', resource: 'custom', description: 'Create custom reports', isSystemPermission: true },
      { module: 'reports', action: 'export', resource: '*', description: 'Export reports', isSystemPermission: true },
      { module: 'reports', action: 'view', resource: 'patient-analytics', description: 'View patient analytics', isSystemPermission: true },
      { module: 'reports', action: 'view', resource: 'appointment-analytics', description: 'View appointment analytics', isSystemPermission: true },
      { module: 'reports', action: 'view', resource: 'financial-analytics', description: 'View financial analytics', isSystemPermission: true },
      { module: 'reports', action: 'view', resource: 'lab-analytics', description: 'View laboratory analytics', isSystemPermission: true },
      { module: 'reports', action: 'view', resource: 'operational', description: 'View operational reports', isSystemPermission: true },
      { module: 'reports', action: 'view', resource: 'clinical', description: 'View clinical reports', isSystemPermission: true },
      { module: 'reports', action: 'view', resource: 'quality', description: 'View quality reports', isSystemPermission: true }
      { module: 'patients', action: 'create', resource: '*', description: 'Create new patients', isSystemPermission: true },
      { module: 'patients', action: 'edit', resource: '*', description: 'Edit patient information', isSystemPermission: true },
      { module: 'patients', action: 'delete', resource: '*', description: 'Delete patients', isSystemPermission: true },
      
      // Appointment permissions
      { module: 'appointments', action: 'view', resource: '*', description: 'View appointments', isSystemPermission: true },
      { module: 'appointments', action: 'create', resource: '*', description: 'Create appointments', isSystemPermission: true },
      { module: 'appointments', action: 'edit', resource: '*', description: 'Edit appointments', isSystemPermission: true },
      { module: 'appointments', action: 'delete', resource: '*', description: 'Delete appointments', isSystemPermission: true },
      
      // Clinical permissions
      { module: 'clinical', action: 'view', resource: '*', description: 'View clinical records', isSystemPermission: true },
      { module: 'clinical', action: 'create', resource: '*', description: 'Create clinical records', isSystemPermission: true },
      { module: 'clinical', action: 'edit', resource: '*', description: 'Edit clinical records', isSystemPermission: true },
      
      // Laboratory permissions
      { module: 'laboratory', action: 'view', resource: '*', description: 'View lab tests', isSystemPermission: true },
      { module: 'laboratory', action: 'create', resource: '*', description: 'Create lab orders', isSystemPermission: true },
      { module: 'laboratory', action: 'edit', resource: '*', description: 'Edit lab results', isSystemPermission: true },
      
      // Pharmacy permissions
      { module: 'pharmacy', action: 'view', resource: '*', description: 'View pharmacy inventory', isSystemPermission: true },
      { module: 'pharmacy', action: 'create', resource: '*', description: 'Create prescriptions', isSystemPermission: true },
      { module: 'pharmacy', action: 'edit', resource: '*', description: 'Edit inventory', isSystemPermission: true },
      
      // Financial permissions
      { module: 'financial', action: 'view', resource: '*', description: 'View financial records', isSystemPermission: true },
      { module: 'financial', action: 'create', resource: '*', description: 'Create bills', isSystemPermission: true },
      { module: 'financial', action: 'edit', resource: '*', description: 'Edit financial records', isSystemPermission: true },
      
      // HR permissions
      { module: 'hr', action: 'view', resource: '*', description: 'View HR records', isSystemPermission: true },
      { module: 'hr', action: 'create', resource: '*', description: 'Create staff records', isSystemPermission: true },
      { module: 'hr', action: 'edit', resource: '*', description: 'Edit staff records', isSystemPermission: true },
      
      // Facility permissions
      { module: 'facility', action: 'view', resource: '*', description: 'View facility management', isSystemPermission: true },
      { module: 'facility', action: 'edit', resource: '*', description: 'Edit facility records', isSystemPermission: true },
      
      // Admin permissions
      { module: 'admin', action: 'view', resource: 'users', description: 'View users', isSystemPermission: true },
      { module: 'admin', action: 'create', resource: 'users', description: 'Create users', isSystemPermission: true },
      { module: 'admin', action: 'edit', resource: 'users', description: 'Edit users', isSystemPermission: true },
      { module: 'admin', action: 'delete', resource: 'users', description: 'Delete users', isSystemPermission: true },
      
      // Reports permissions
      { module: 'reports', action: 'view', resource: '*', description: 'View reports', isSystemPermission: true },
      { module: 'reports', action: 'export', resource: '*', description: 'Export reports', isSystemPermission: true }
    ];

    const createdPermissions = await Permission.insertMany(permissions);
    console.log(`✅ Created ${createdPermissions.length} permissions`);

    // Create roles with permissions
    const allPermissionIds = createdPermissions.map(p => p._id);
    
    const roles = [
      {
        name: 'Super Admin',
        description: 'Full system access',
        level: 10,
        isSystemRole: true,
        defaultPermissions: allPermissionIds
      },
      {
        name: 'Hospital Administrator',
        description: 'Hospital management access',
        level: 9,
        isSystemRole: true,
        defaultPermissions: allPermissionIds.filter(id => {
          const permission = createdPermissions.find(p => p._id.equals(id));
          return permission && !['admin'].includes(permission.module);
        })
      },
      {
        name: 'Doctor',
        description: 'Medical staff access',
        level: 7,
        isSystemRole: true,
        defaultPermissions: allPermissionIds.filter(id => {
          const permission = createdPermissions.find(p => p._id.equals(id));
          return permission && ['dashboard', 'patients', 'appointments', 'clinical', 'laboratory', 'pharmacy'].includes(permission.module);
        })
      },
      {
        name: 'Nurse',
        description: 'Nursing staff access',
        level: 5,
        isSystemRole: true,
        defaultPermissions: allPermissionIds.filter(id => {
          const permission = createdPermissions.find(p => p._id.equals(id));
          return permission && ['dashboard', 'patients', 'appointments', 'clinical'].includes(permission.module) && permission.action !== 'delete';
        })
      },
      {
        name: 'Receptionist',
        description: 'Front desk access',
        level: 3,
        isSystemRole: true,
        defaultPermissions: allPermissionIds.filter(id => {
          const permission = createdPermissions.find(p => p._id.equals(id));
          return permission && ['dashboard', 'patients', 'appointments'].includes(permission.module);
        })
      },
      {
        name: 'Lab Technician',
        description: 'Laboratory access',
        level: 4,
        isSystemRole: true,
        defaultPermissions: allPermissionIds.filter(id => {
          const permission = createdPermissions.find(p => p._id.equals(id));
          return permission && ['dashboard', 'laboratory', 'patients'].includes(permission.module);
        })
      },
      {
        name: 'Pharmacist',
        description: 'Pharmacy access',
        level: 4,
        isSystemRole: true,
        defaultPermissions: allPermissionIds.filter(id => {
          const permission = createdPermissions.find(p => p._id.equals(id));
          return permission && ['dashboard', 'pharmacy', 'patients'].includes(permission.module);
        })
      }
    ];

    const createdRoles = await Role.insertMany(roles);
    console.log(`✅ Created ${createdRoles.length} roles`);

    // Create default admin user
    const superAdminRole = createdRoles.find(r => r.name === 'Super Admin');
    const doctorRole = createdRoles.find(r => r.name === 'Doctor');
    const nurseRole = createdRoles.find(r => r.name === 'Nurse');

    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'System',
        lastName: 'Administrator',
        role: superAdminRole!._id,
        permissions: superAdminRole!.defaultPermissions,
        department: 'Administration',
        isActive: true
      },
      {
        username: 'dr.smith',
        email: '<EMAIL>',
        password: 'doctor123',
        firstName: 'John',
        lastName: 'Smith',
        role: doctorRole!._id,
        permissions: doctorRole!.defaultPermissions,
        department: 'Cardiology',
        isActive: true
      },
      {
        username: 'nurse.jane',
        email: '<EMAIL>',
        password: 'nurse123',
        firstName: 'Jane',
        lastName: 'Doe',
        role: nurseRole!._id,
        permissions: nurseRole!.defaultPermissions,
        department: 'General Ward',
        isActive: true
      }
    ];

    const createdUsers = await User.insertMany(users);
    console.log(`✅ Created ${createdUsers.length} users`);

    // Create sample patients
    const samplePatients = [
      {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: new Date('1985-06-15'),
        gender: 'Male',
        email: '<EMAIL>',
        phone: '******-0101',
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        emergencyContact: {
          name: 'Jane Doe',
          relationship: 'Spouse',
          phone: '******-0102'
        },
        bloodType: 'O+',
        status: 'Active'
      },
      {
        firstName: 'Sarah',
        lastName: 'Johnson',
        dateOfBirth: new Date('1992-03-22'),
        gender: 'Female',
        email: '<EMAIL>',
        phone: '******-0201',
        address: {
          street: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90210',
          country: 'USA'
        },
        emergencyContact: {
          name: 'Mike Johnson',
          relationship: 'Brother',
          phone: '******-0202'
        },
        bloodType: 'A+',
        status: 'Active'
      }
    ];

    const createdPatients = await Patient.insertMany(samplePatients);
    console.log(`✅ Created ${createdPatients.length} sample patients`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Default Login Credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Doctor: <EMAIL> / doctor123');
    console.log('Nurse: <EMAIL> / nurse123');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
};

// Run seeding if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}
