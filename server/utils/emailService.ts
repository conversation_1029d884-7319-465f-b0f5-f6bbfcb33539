import nodemailer from 'nodemailer';

interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
}

// Create transporter
const createTransporter = () => {
  if (process.env.NODE_ENV === 'production') {
    // Production email configuration (e.g., SendGrid, AWS SES, etc.)
    return nodemailer.createTransporter({
      service: process.env.EMAIL_SERVICE || 'gmail',
      auth: {
        user: process.env.EMAIL_USERNAME,
        pass: process.env.EMAIL_PASSWORD,
      },
    });
  } else {
    // Development configuration (using Ethereal for testing)
    return nodemailer.createTransporter({
      host: 'smtp.ethereal.email',
      port: 587,
      auth: {
        user: '<EMAIL>',
        pass: 'ethereal.pass',
      },
    });
  }
};

export const sendEmail = async (options: EmailOptions): Promise<void> => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: `${process.env.FROM_NAME || 'Hospital Management System'} <${process.env.FROM_EMAIL || '<EMAIL>'}>`,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
    };

    const info = await transporter.sendMail(mailOptions);
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Message sent: %s', info.messageId);
      console.log('Preview URL: %s', nodemailer.getTestMessageUrl(info));
    }
  } catch (error) {
    console.error('Email sending error:', error);
    throw new Error('Email could not be sent');
  }
};

export const sendAppointmentConfirmation = async (
  patientEmail: string,
  patientName: string,
  appointmentDetails: {
    date: string;
    time: string;
    doctor: string;
    department: string;
    location: string;
  }
) => {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2c3e50;">Appointment Confirmation</h2>
      <p>Dear ${patientName},</p>
      <p>Your appointment has been confirmed with the following details:</p>
      
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #495057;">Appointment Details</h3>
        <p><strong>Date:</strong> ${appointmentDetails.date}</p>
        <p><strong>Time:</strong> ${appointmentDetails.time}</p>
        <p><strong>Doctor:</strong> ${appointmentDetails.doctor}</p>
        <p><strong>Department:</strong> ${appointmentDetails.department}</p>
        <p><strong>Location:</strong> ${appointmentDetails.location}</p>
      </div>
      
      <p><strong>Important Instructions:</strong></p>
      <ul>
        <li>Please arrive 15 minutes before your appointment time</li>
        <li>Bring a valid ID and insurance card</li>
        <li>Bring any relevant medical records or test results</li>
        <li>If you need to cancel or reschedule, please call us at least 24 hours in advance</li>
      </ul>
      
      <p>If you have any questions, please contact us at <a href="tel:+**********">(*************</a></p>
      
      <p>Thank you for choosing our hospital.</p>
      <p>Best regards,<br>Hospital Management Team</p>
    </div>
  `;

  await sendEmail({
    to: patientEmail,
    subject: 'Appointment Confirmation - Hospital Management System',
    html
  });
};

export const sendAppointmentReminder = async (
  patientEmail: string,
  patientName: string,
  appointmentDetails: {
    date: string;
    time: string;
    doctor: string;
    department: string;
  }
) => {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2c3e50;">Appointment Reminder</h2>
      <p>Dear ${patientName},</p>
      <p>This is a reminder that you have an upcoming appointment:</p>
      
      <div style="background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
        <h3 style="margin-top: 0; color: #856404;">Tomorrow's Appointment</h3>
        <p><strong>Date:</strong> ${appointmentDetails.date}</p>
        <p><strong>Time:</strong> ${appointmentDetails.time}</p>
        <p><strong>Doctor:</strong> ${appointmentDetails.doctor}</p>
        <p><strong>Department:</strong> ${appointmentDetails.department}</p>
      </div>
      
      <p>Please remember to arrive 15 minutes early and bring your ID and insurance card.</p>
      <p>If you need to cancel or reschedule, please call us as soon as possible.</p>
      
      <p>Thank you,<br>Hospital Management Team</p>
    </div>
  `;

  await sendEmail({
    to: patientEmail,
    subject: 'Appointment Reminder - Tomorrow',
    html
  });
};
