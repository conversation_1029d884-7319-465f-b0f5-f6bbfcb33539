import PDFDocument from 'pdfkit';

// Hospital Configuration
const HOSPITAL_CONFIG = {
  name: 'HOSPITAL MANAGEMENT SYSTEM',
  address: '123 Medical Center Drive',
  city: 'Healthcare City, HC 12345',
  phone: '+****************',
  email: '<EMAIL>',
  website: 'www.hospital.com',
  logo: null
};

// Color Scheme
const COLORS = {
  primary: '#2563eb',
  secondary: '#1e40af',
  accent: '#3b82f6',
  success: '#059669',
  warning: '#d97706',
  danger: '#dc2626',
  dark: '#1f2937',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  white: '#ffffff'
};

export async function createFinancialReportPDF(reportType, reportData, options) {
  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ 
        margin: 40,
        size: 'A4',
        info: {
          Title: `${reportType} - ${options.startDate.toLocaleDateString()} to ${options.endDate.toLocaleDateString()}`,
          Author: 'Hospital Management System',
          Subject: `Financial Report - ${reportType}`,
          Creator: 'HMS Financial Module'
        }
      });
      
      const chunks = [];
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);

      // Generate report based on type
      switch (reportType) {
        case 'Revenue Report':
          generateRevenueReportPDF(doc, reportData, options);
          break;
        case 'Payment Report':
          generatePaymentReportPDF(doc, reportData, options);
          break;
        case 'Outstanding Report':
          generateOutstandingReportPDF(doc, reportData, options);
          break;
        case 'Department Revenue':
          generateDepartmentReportPDF(doc, reportData, options);
          break;
        case 'Aging Report':
          generateAgingReportPDF(doc, reportData, options);
          break;
        default:
          throw new Error('Unknown report type');
      }

      doc.end();
    } catch (error) {
      reject(error);
    }
  });
}

// Common PDF utilities
function addHeader(doc, reportType, options) {
  const pageWidth = doc.page.width;
  const margin = doc.page.margins.left;
  
  // Header background
  doc.rect(0, 0, pageWidth, 120)
     .fillColor(COLORS.primary)
     .fill();

  // Hospital name
  doc.fillColor(COLORS.white)
     .fontSize(24)
     .font('Helvetica-Bold')
     .text(HOSPITAL_CONFIG.name, margin, 25, { width: pageWidth - 2 * margin, align: 'center' });

  // Hospital details
  doc.fontSize(10)
     .font('Helvetica')
     .text(`${HOSPITAL_CONFIG.address} | ${HOSPITAL_CONFIG.city}`, margin, 55, { width: pageWidth - 2 * margin, align: 'center' })
     .text(`Phone: ${HOSPITAL_CONFIG.phone} | Email: ${HOSPITAL_CONFIG.email}`, margin, 70, { width: pageWidth - 2 * margin, align: 'center' });

  // Report title
  doc.fontSize(18)
     .font('Helvetica-Bold')
     .text(reportType, margin, 90, { width: pageWidth - 2 * margin, align: 'center' });

  // Reset position
  doc.y = 140;
  doc.fillColor(COLORS.dark);
}

function addReportInfo(doc, options) {
  const startY = doc.y;
  
  // Report info box
  doc.rect(doc.page.margins.left, startY, doc.page.width - 2 * doc.page.margins.left, 60)
     .fillColor(COLORS.lightGray)
     .fill()
     .strokeColor(COLORS.gray)
     .stroke();

  doc.fillColor(COLORS.dark)
     .fontSize(10)
     .font('Helvetica-Bold')
     .text('Report Period:', doc.page.margins.left + 20, startY + 15)
     .font('Helvetica')
     .text(`${options.startDate.toLocaleDateString()} to ${options.endDate.toLocaleDateString()}`, doc.page.margins.left + 100, startY + 15);

  doc.font('Helvetica-Bold')
     .text('Generated On:', doc.page.margins.left + 20, startY + 30)
     .font('Helvetica')
     .text(new Date().toLocaleString(), doc.page.margins.left + 100, startY + 30);

  doc.font('Helvetica-Bold')
     .text('Generated By:', doc.page.margins.left + 20, startY + 45)
     .font('Helvetica')
     .text(options.generatedBy || 'System', doc.page.margins.left + 100, startY + 45);

  doc.y = startY + 80;
}

function addSummaryCards(doc, summaryData) {
  const cardWidth = 120;
  const cardHeight = 80;
  const spacing = 20;
  const startX = doc.page.margins.left;
  let currentX = startX;
  const startY = doc.y;

  summaryData.forEach((card, index) => {
    if (currentX + cardWidth > doc.page.width - doc.page.margins.right) {
      currentX = startX;
      doc.y += cardHeight + spacing;
    }

    // Card background
    doc.rect(currentX, doc.y, cardWidth, cardHeight)
       .fillColor(card.color || COLORS.lightGray)
       .fill()
       .strokeColor(COLORS.gray)
       .stroke();

    // Card content
    doc.fillColor(COLORS.white)
       .fontSize(12)
       .font('Helvetica-Bold')
       .text(card.title, currentX + 10, doc.y + 15, { width: cardWidth - 20, align: 'center' });

    doc.fontSize(18)
       .text(card.value, currentX + 10, doc.y + 35, { width: cardWidth - 20, align: 'center' });

    if (card.subtitle) {
      doc.fontSize(8)
         .font('Helvetica')
         .text(card.subtitle, currentX + 10, doc.y + 60, { width: cardWidth - 20, align: 'center' });
    }

    currentX += cardWidth + spacing;
  });

  doc.y += cardHeight + 30;
  doc.fillColor(COLORS.dark);
}

function addTable(doc, headers, rows, options = {}) {
  const tableTop = doc.y;
  const tableLeft = doc.page.margins.left;
  const tableWidth = doc.page.width - 2 * doc.page.margins.left;
  const rowHeight = options.rowHeight || 25;
  const headerHeight = options.headerHeight || 30;
  
  // Calculate column widths
  const colWidth = tableWidth / headers.length;
  
  // Header background
  doc.rect(tableLeft, tableTop, tableWidth, headerHeight)
     .fillColor(COLORS.primary)
     .fill();

  // Header text
  doc.fillColor(COLORS.white)
     .fontSize(10)
     .font('Helvetica-Bold');
  
  headers.forEach((header, i) => {
    doc.text(header, tableLeft + i * colWidth + 5, tableTop + 8, {
      width: colWidth - 10,
      align: 'left'
    });
  });

  // Table rows
  doc.fillColor(COLORS.dark)
     .font('Helvetica')
     .fontSize(9);

  rows.forEach((row, rowIndex) => {
    const y = tableTop + headerHeight + rowIndex * rowHeight;
    
    // Alternate row background
    if (rowIndex % 2 === 1) {
      doc.rect(tableLeft, y, tableWidth, rowHeight)
         .fillColor('#f9fafb')
         .fill();
      doc.fillColor(COLORS.dark);
    }

    // Row data
    row.forEach((cell, colIndex) => {
      doc.text(String(cell), tableLeft + colIndex * colWidth + 5, y + 5, {
        width: colWidth - 10,
        align: colIndex === 0 ? 'left' : 'right'
      });
    });
  });

  // Table border
  doc.rect(tableLeft, tableTop, tableWidth, headerHeight + rows.length * rowHeight)
     .strokeColor(COLORS.gray)
     .stroke();

  doc.y = tableTop + headerHeight + rows.length * rowHeight + 20;
}

function addChart(doc, title, data, type = 'bar') {
  const chartWidth = 400;
  const chartHeight = 200;
  const chartX = (doc.page.width - chartWidth) / 2;
  const chartY = doc.y;

  // Chart title
  doc.fontSize(14)
     .font('Helvetica-Bold')
     .fillColor(COLORS.dark)
     .text(title, chartX, chartY, { width: chartWidth, align: 'center' });

  // Chart background
  doc.rect(chartX, chartY + 30, chartWidth, chartHeight)
     .fillColor(COLORS.lightGray)
     .stroke();

  // Simple bar chart representation
  if (type === 'bar' && data.length > 0) {
    const maxValue = Math.max(...data.map(d => d.value));
    const barWidth = (chartWidth - 40) / data.length;
    
    data.forEach((item, index) => {
      const barHeight = (item.value / maxValue) * (chartHeight - 40);
      const x = chartX + 20 + index * barWidth;
      const y = chartY + 30 + chartHeight - 20 - barHeight;
      
      // Bar
      doc.rect(x + 5, y, barWidth - 10, barHeight)
         .fillColor(COLORS.accent)
         .fill();
      
      // Label
      doc.fontSize(8)
         .fillColor(COLORS.dark)
         .text(item.label, x, chartY + 30 + chartHeight - 15, {
           width: barWidth,
           align: 'center'
         });
    });
  }

  doc.y = chartY + chartHeight + 50;
}

// Revenue Report PDF
function generateRevenueReportPDF(doc, data, options) {
  addHeader(doc, 'Revenue Report', options);
  addReportInfo(doc, options);

  // Summary cards
  const summaryCards = [
    {
      title: 'Total Revenue',
      value: `$${data.summary.totalRevenue.toLocaleString()}`,
      color: COLORS.success
    },
    {
      title: 'Total Paid',
      value: `$${data.summary.totalPaid.toLocaleString()}`,
      color: COLORS.primary
    },
    {
      title: 'Outstanding',
      value: `$${data.summary.totalOutstanding.toLocaleString()}`,
      color: COLORS.warning
    },
    {
      title: 'Collection Rate',
      value: `${data.summary.collectionRate.toFixed(1)}%`,
      color: COLORS.accent
    }
  ];

  addSummaryCards(doc, summaryCards);

  // Department breakdown chart
  if (data.departmentBreakdown.length > 0) {
    const chartData = data.departmentBreakdown.slice(0, 5).map(dept => ({
      label: dept.department,
      value: dept.revenue
    }));
    addChart(doc, 'Revenue by Department', chartData);
  }

  // Department breakdown table
  if (data.departmentBreakdown.length > 0) {
    doc.fontSize(14)
       .font('Helvetica-Bold')
       .text('Department Revenue Breakdown', doc.page.margins.left, doc.y);
    
    doc.y += 20;

    const headers = ['Department', 'Revenue', 'Bills', 'Paid', 'Outstanding', '%'];
    const rows = data.departmentBreakdown.map(dept => [
      dept.department,
      `$${dept.revenue.toLocaleString()}`,
      dept.count.toString(),
      `$${dept.paid.toLocaleString()}`,
      `$${(dept.revenue - dept.paid).toLocaleString()}`,
      `${dept.percentage.toFixed(1)}%`
    ]);

    addTable(doc, headers, rows);
  }
}

// Payment Report PDF
function generatePaymentReportPDF(doc, data, options) {
  addHeader(doc, 'Payment Report', options);
  addReportInfo(doc, options);

  // Summary cards
  const summaryCards = [
    {
      title: 'Total Payments',
      value: `$${data.summary.totalPayments.toLocaleString()}`,
      color: COLORS.success
    },
    {
      title: 'Payment Count',
      value: data.summary.paymentCount.toString(),
      color: COLORS.primary
    },
    {
      title: 'Average Payment',
      value: `$${data.summary.averagePayment.toLocaleString()}`,
      color: COLORS.accent
    }
  ];

  addSummaryCards(doc, summaryCards);

  // Payment method breakdown
  if (data.methodBreakdown.length > 0) {
    doc.fontSize(14)
       .font('Helvetica-Bold')
       .text('Payment Method Breakdown', doc.page.margins.left, doc.y);
    
    doc.y += 20;

    const headers = ['Payment Method', 'Amount', 'Count', 'Percentage'];
    const rows = data.methodBreakdown.map(method => [
      method.method,
      `$${method.amount.toLocaleString()}`,
      method.count.toString(),
      `${method.percentage.toFixed(1)}%`
    ]);

    addTable(doc, headers, rows);
  }
}

// Outstanding Report PDF
function generateOutstandingReportPDF(doc, data, options) {
  addHeader(doc, 'Outstanding Report', options);
  addReportInfo(doc, options);

  // Summary cards
  const summaryCards = [
    {
      title: 'Total Outstanding',
      value: `$${data.summary.totalOutstanding.toLocaleString()}`,
      color: COLORS.danger
    },
    {
      title: 'Outstanding Bills',
      value: data.summary.billCount.toString(),
      color: COLORS.warning
    },
    {
      title: 'Average Outstanding',
      value: `$${data.summary.averageOutstanding.toLocaleString()}`,
      color: COLORS.accent
    }
  ];

  addSummaryCards(doc, summaryCards);

  // Age analysis table
  if (data.ageAnalysis.length > 0) {
    doc.fontSize(14)
       .font('Helvetica-Bold')
       .text('Outstanding Age Analysis', doc.page.margins.left, doc.y);
    
    doc.y += 20;

    const headers = ['Age Category', 'Amount', 'Bills', 'Percentage'];
    const rows = data.ageAnalysis.map(age => [
      age.category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
      `$${age.amount.toLocaleString()}`,
      age.count.toString(),
      `${age.percentage.toFixed(1)}%`
    ]);

    addTable(doc, headers, rows);
  }
}

// Department Report PDF
function generateDepartmentReportPDF(doc, data, options) {
  addHeader(doc, 'Department Revenue Report', options);
  addReportInfo(doc, options);

  // Summary cards
  const summaryCards = [
    {
      title: 'Total Revenue',
      value: `$${data.summary.totalRevenue.toLocaleString()}`,
      color: COLORS.success
    },
    {
      title: 'Departments',
      value: data.summary.departmentCount.toString(),
      color: COLORS.primary
    }
  ];

  addSummaryCards(doc, summaryCards);

  // Department performance table
  if (data.departments.length > 0) {
    doc.fontSize(14)
       .font('Helvetica-Bold')
       .text('Department Performance', doc.page.margins.left, doc.y);
    
    doc.y += 20;

    const headers = ['Department', 'Revenue', 'Bills', 'Avg Bill', 'Collection %', 'Revenue %'];
    const rows = data.departments.map(dept => [
      dept.name,
      `$${dept.revenue.toLocaleString()}`,
      dept.billCount.toString(),
      `$${dept.averageBill.toLocaleString()}`,
      `${dept.collectionRate.toFixed(1)}%`,
      `${dept.revenuePercentage.toFixed(1)}%`
    ]);

    addTable(doc, headers, rows);
  }
}

// Aging Report PDF
function generateAgingReportPDF(doc, data, options) {
  addHeader(doc, 'Accounts Receivable Aging Report', options);
  addReportInfo(doc, options);

  // Summary cards
  const summaryCards = [
    {
      title: 'Total Outstanding',
      value: `$${data.totalOutstanding.toLocaleString()}`,
      color: COLORS.danger
    },
    {
      title: 'Total Bills',
      value: data.totalBills.toString(),
      color: COLORS.warning
    }
  ];

  addSummaryCards(doc, summaryCards);

  // Aging summary table
  const agingLabels = {
    current: 'Current (0 days)',
    overdue30: '1-30 days overdue',
    overdue60: '31-60 days overdue',
    overdue90: '61-90 days overdue',
    overdue90Plus: '90+ days overdue'
  };

  doc.fontSize(14)
     .font('Helvetica-Bold')
     .text('Aging Summary', doc.page.margins.left, doc.y);
  
  doc.y += 20;

  const headers = ['Age Category', 'Amount', 'Bills', 'Percentage'];
  const rows = Object.entries(data.summary).map(([category, summary]) => [
    agingLabels[category] || category,
    `$${summary.amount.toLocaleString()}`,
    summary.count.toString(),
    `${data.totalOutstanding > 0 ? ((summary.amount / data.totalOutstanding) * 100).toFixed(1) : 0}%`
  ]);

  addTable(doc, headers, rows);
}
