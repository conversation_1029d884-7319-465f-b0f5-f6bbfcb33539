import mongoose from 'mongoose';

// Lab Test schema
const LabTestSchema = new mongoose.Schema({
  testId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'LAB' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  testName: {
    type: String,
    required: true,
    trim: true
  },
  testCategory: {
    type: String,
    required: true,
    enum: [
      'Blood Test',
      'Urine Test',
      'Stool Test',
      'Imaging',
      'Biopsy',
      'Microbiology',
      'Biochemistry',
      'Hematology',
      'Immunology',
      'Pathology',
      'Genetics'
    ]
  },
  testCode: {
    type: String,
    required: true,
    trim: true
  },
  priority: {
    type: String,
    required: true,
    enum: ['Routine', 'Urgent', 'STAT', 'Emergency'],
    default: 'Routine'
  },
  status: {
    type: String,
    required: true,
    enum: ['Ordered', 'Sample Collected', 'In Progress', 'Completed', 'Cancelled', 'Failed'],
    default: 'Ordered'
  },
  orderDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  sampleCollectionDate: Date,
  expectedResultDate: Date,
  completedDate: Date,
  results: {
    values: [{
      parameter: { type: String, required: true },
      value: { type: String, required: true },
      unit: String,
      normalRange: String,
      status: {
        type: String,
        enum: ['Normal', 'Abnormal', 'Critical', 'High', 'Low'],
        default: 'Normal'
      },
      notes: String
    }],
    interpretation: String,
    conclusion: String,
    recommendations: [String]
  },
  technician: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: Date,
  notes: String,
  attachments: [{
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    uploadDate: { type: Date, default: Date.now }
  }],
  cost: {
    type: Number,
    min: 0
  },
  insurance: {
    covered: { type: Boolean, default: false },
    claimNumber: String,
    approvedAmount: Number
  }
}, {
  timestamps: true
});

// Indexes
LabTestSchema.index({ testId: 1 });
LabTestSchema.index({ patient: 1 });
LabTestSchema.index({ doctor: 1 });
LabTestSchema.index({ status: 1 });
LabTestSchema.index({ testCategory: 1 });
LabTestSchema.index({ orderDate: 1 });
LabTestSchema.index({ priority: 1 });

// Get existing model or create new one
let LabTest;
try {
  LabTest = mongoose.model('LabTest');
} catch (error) {
  LabTest = mongoose.model('LabTest', LabTestSchema);
}

// @desc    Get all lab tests with pagination and filtering
// @route   GET /api/laboratory/tests
// @access  Private
export const getLabTests = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.category) {
      filter.testCategory = req.query.category;
    }
    
    if (req.query.priority) {
      filter.priority = req.query.priority;
    }
    
    if (req.query.patient) {
      filter.patient = req.query.patient;
    }
    
    if (req.query.doctor) {
      filter.doctor = req.query.doctor;
    }
    
    if (req.query.dateFrom || req.query.dateTo) {
      filter.orderDate = {};
      if (req.query.dateFrom) {
        filter.orderDate.$gte = new Date(req.query.dateFrom);
      }
      if (req.query.dateTo) {
        filter.orderDate.$lte = new Date(req.query.dateTo);
      }
    }

    const tests = await LabTest.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .populate('technician', 'firstName lastName')
      .populate('reviewedBy', 'firstName lastName')
      .sort({ orderDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await LabTest.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: tests,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get lab tests error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching lab tests'
    });
  }
};

// @desc    Get single lab test
// @route   GET /api/laboratory/tests/:id
// @access  Private
export const getLabTest = async (req, res) => {
  try {
    const test = await LabTest.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId phone email dateOfBirth gender')
      .populate('doctor', 'firstName lastName department email')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime')
      .populate('technician', 'firstName lastName email')
      .populate('reviewedBy', 'firstName lastName email');

    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Lab test not found'
      });
    }

    res.status(200).json({
      success: true,
      data: test
    });
  } catch (error) {
    console.error('Get lab test error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching lab test'
    });
  }
};

// @desc    Create new lab test order
// @route   POST /api/laboratory/tests
// @access  Private
export const createLabTest = async (req, res) => {
  try {
    // Calculate expected result date based on test type
    const expectedDays = getExpectedDays(req.body.testCategory);
    const expectedResultDate = new Date();
    expectedResultDate.setDate(expectedResultDate.getDate() + expectedDays);
    
    req.body.expectedResultDate = expectedResultDate;

    const test = await LabTest.create(req.body);

    // Populate the created test
    const populatedTest = await LabTest.findById(test._id)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime');

    res.status(201).json({
      success: true,
      data: populatedTest
    });
  } catch (error) {
    console.error('Create lab test error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating lab test'
    });
  }
};

// @desc    Update lab test
// @route   PUT /api/laboratory/tests/:id
// @access  Private
export const updateLabTest = async (req, res) => {
  try {
    const test = await LabTest.findById(req.params.id);

    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Lab test not found'
      });
    }

    // Update completion date if status is being changed to completed
    if (req.body.status === 'Completed' && test.status !== 'Completed') {
      req.body.completedDate = new Date();
    }

    const updatedTest = await LabTest.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .populate('technician', 'firstName lastName')
      .populate('reviewedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedTest
    });
  } catch (error) {
    console.error('Update lab test error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating lab test'
    });
  }
};

// @desc    Delete lab test
// @route   DELETE /api/laboratory/tests/:id
// @access  Private
export const deleteLabTest = async (req, res) => {
  try {
    const test = await LabTest.findById(req.params.id);

    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Lab test not found'
      });
    }

    // Cancel instead of delete if not yet completed
    if (test.status !== 'Completed') {
      test.status = 'Cancelled';
      await test.save();
      
      res.status(200).json({
        success: true,
        message: 'Lab test cancelled successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Cannot delete completed lab test'
      });
    }
  } catch (error) {
    console.error('Delete lab test error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while cancelling lab test'
    });
  }
};

// @desc    Get patient's lab history
// @route   GET /api/laboratory/patients/:patientId/tests
// @access  Private
export const getPatientLabHistory = async (req, res) => {
  try {
    const { patientId } = req.params;

    const tests = await LabTest.find({ patient: patientId })
      .populate('doctor', 'firstName lastName department')
      .populate('technician', 'firstName lastName')
      .sort({ orderDate: -1 });

    res.status(200).json({
      success: true,
      data: tests
    });
  } catch (error) {
    console.error('Get patient lab history error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patient lab history'
    });
  }
};

// @desc    Get lab statistics
// @route   GET /api/laboratory/stats
// @access  Private
export const getLabStats = async (req, res) => {
  try {
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    const stats = await Promise.all([
      // Today's tests
      LabTest.countDocuments({
        orderDate: { $gte: startOfDay, $lte: endOfDay }
      }),
      // Pending tests
      LabTest.countDocuments({
        status: { $in: ['Ordered', 'Sample Collected', 'In Progress'] }
      }),
      // Completed today
      LabTest.countDocuments({
        completedDate: { $gte: startOfDay, $lte: endOfDay }
      }),
      // Critical results
      LabTest.countDocuments({
        'results.values.status': 'Critical',
        status: 'Completed'
      })
    ]);

    res.status(200).json({
      success: true,
      data: {
        todaysTests: stats[0],
        pendingTests: stats[1],
        completedToday: stats[2],
        criticalResults: stats[3]
      }
    });
  } catch (error) {
    console.error('Get lab stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching lab statistics'
    });
  }
};

// Helper function to get expected days for test completion
const getExpectedDays = (testCategory) => {
  const expectedDays = {
    'Blood Test': 1,
    'Urine Test': 1,
    'Stool Test': 2,
    'Imaging': 1,
    'Biopsy': 7,
    'Microbiology': 3,
    'Biochemistry': 1,
    'Hematology': 1,
    'Immunology': 2,
    'Pathology': 5,
    'Genetics': 14
  };
  
  return expectedDays[testCategory] || 2;
};
