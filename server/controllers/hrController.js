import mongoose from 'mongoose';

// Staff Schedule schema
const StaffScheduleSchema = new mongoose.Schema({
  scheduleId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'SCH' + Date.now().toString().slice(-6);
    }
  },
  staff: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  shift: {
    type: String,
    required: true,
    enum: ['Morning', 'Afternoon', 'Night', 'Full Day']
  },
  startTime: {
    type: String,
    required: true
  },
  endTime: {
    type: String,
    required: true
  },
  department: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['Scheduled', 'Confirmed', 'Completed', 'Cancelled', 'No Show'],
    default: 'Scheduled'
  },
  notes: String,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Staff Performance schema
const StaffPerformanceSchema = new mongoose.Schema({
  performanceId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'PERF' + Date.now().toString().slice(-6);
    }
  },
  staff: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  evaluationPeriod: {
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true }
  },
  metrics: {
    punctuality: { type: Number, min: 1, max: 5 },
    productivity: { type: Number, min: 1, max: 5 },
    teamwork: { type: Number, min: 1, max: 5 },
    communication: { type: Number, min: 1, max: 5 },
    patientCare: { type: Number, min: 1, max: 5 }
  },
  overallRating: {
    type: Number,
    min: 1,
    max: 5
  },
  achievements: [String],
  areasForImprovement: [String],
  goals: [String],
  comments: String,
  evaluatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['Draft', 'Completed', 'Reviewed', 'Approved'],
    default: 'Draft'
  }
}, {
  timestamps: true
});

// Indexes
StaffScheduleSchema.index({ staff: 1, date: 1 });
StaffScheduleSchema.index({ department: 1, date: 1 });
StaffPerformanceSchema.index({ staff: 1 });
StaffPerformanceSchema.index({ 'evaluationPeriod.startDate': 1, 'evaluationPeriod.endDate': 1 });

// Get existing models or create new ones
let StaffSchedule, StaffPerformance;
try {
  StaffSchedule = mongoose.model('StaffSchedule');
  StaffPerformance = mongoose.model('StaffPerformance');
} catch (error) {
  StaffSchedule = mongoose.model('StaffSchedule', StaffScheduleSchema);
  StaffPerformance = mongoose.model('StaffPerformance', StaffPerformanceSchema);
}

// @desc    Get all staff members
// @route   GET /api/hr/staff
// @access  Private
export const getStaff = async (req, res) => {
  try {
    const User = mongoose.model('User');
    const Role = mongoose.model('Role');
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = {};

    if (req.query.department) {
      filter.department = req.query.department;
    }

    // Handle role filtering by name
    if (req.query.role) {
      const role = await Role.findOne({ name: req.query.role });
      if (role) {
        filter.role = role._id;
      } else {
        // If role not found, return empty result
        return res.status(200).json({
          success: true,
          data: [],
          pagination: {
            page,
            limit,
            total: 0,
            pages: 0
          }
        });
      }
    }

    const staff = await User.find(filter)
      .populate('role', 'name description')
      .select('-password')
      .sort({ firstName: 1, lastName: 1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: staff,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get staff error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching staff'
    });
  }
};

// @desc    Get staff schedules
// @route   GET /api/hr/schedules
// @access  Private
export const getStaffSchedules = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = {};
    
    if (req.query.staff) {
      filter.staff = req.query.staff;
    }
    
    if (req.query.department) {
      filter.department = req.query.department;
    }
    
    if (req.query.date) {
      const startDate = new Date(req.query.date);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);
      filter.date = {
        $gte: startDate,
        $lt: endDate
      };
    }

    const schedules = await StaffSchedule.find(filter)
      .populate('staff', 'firstName lastName email department')
      .populate('createdBy', 'firstName lastName')
      .sort({ date: 1, startTime: 1 })
      .skip(skip)
      .limit(limit);

    const total = await StaffSchedule.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: schedules,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get staff schedules error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching staff schedules'
    });
  }
};

// @desc    Create staff schedule
// @route   POST /api/hr/schedules
// @access  Private
export const createStaffSchedule = async (req, res) => {
  try {
    req.body.createdBy = req.user._id;

    const schedule = await StaffSchedule.create(req.body);

    const populatedSchedule = await StaffSchedule.findById(schedule._id)
      .populate('staff', 'firstName lastName email department')
      .populate('createdBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedSchedule
    });
  } catch (error) {
    console.error('Create staff schedule error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating staff schedule'
    });
  }
};

// @desc    Get staff performance evaluations
// @route   GET /api/hr/performance
// @access  Private
export const getStaffPerformance = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = {};
    
    if (req.query.staff) {
      filter.staff = req.query.staff;
    }
    
    if (req.query.status) {
      filter.status = req.query.status;
    }

    const evaluations = await StaffPerformance.find(filter)
      .populate('staff', 'firstName lastName email department')
      .populate('evaluatedBy', 'firstName lastName')
      .sort({ 'evaluationPeriod.endDate': -1 })
      .skip(skip)
      .limit(limit);

    const total = await StaffPerformance.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: evaluations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get staff performance error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching staff performance'
    });
  }
};

// @desc    Create staff performance evaluation
// @route   POST /api/hr/performance
// @access  Private
export const createStaffPerformance = async (req, res) => {
  try {
    req.body.evaluatedBy = req.user._id;

    // Calculate overall rating
    const metrics = req.body.metrics;
    if (metrics) {
      const ratings = Object.values(metrics).filter(rating => typeof rating === 'number');
      req.body.overallRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
    }

    const evaluation = await StaffPerformance.create(req.body);

    const populatedEvaluation = await StaffPerformance.findById(evaluation._id)
      .populate('staff', 'firstName lastName email department')
      .populate('evaluatedBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedEvaluation
    });
  } catch (error) {
    console.error('Create staff performance error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating staff performance evaluation'
    });
  }
};

// @desc    Get HR statistics
// @route   GET /api/hr/stats
// @access  Private
export const getHRStats = async (req, res) => {
  try {
    const User = mongoose.model('User');
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    const stats = await Promise.all([
      // Total active staff
      User.countDocuments({ isActive: true }),
      // Staff on duty today
      StaffSchedule.countDocuments({
        date: { $gte: startOfDay, $lte: endOfDay },
        status: { $in: ['Scheduled', 'Confirmed'] }
      }),
      // Pending performance evaluations
      StaffPerformance.countDocuments({ status: 'Draft' }),
      // Staff by department
      User.aggregate([
        {
          $match: { isActive: true }
        },
        {
          $group: {
            _id: '$department',
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalStaff: stats[0],
        staffOnDutyToday: stats[1],
        pendingEvaluations: stats[2],
        staffByDepartment: stats[3]
      }
    });
  } catch (error) {
    console.error('Get HR stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching HR statistics'
    });
  }
};
