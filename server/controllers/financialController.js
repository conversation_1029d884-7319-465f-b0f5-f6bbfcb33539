import mongoose from 'mongoose';
import Bill from '../models/Bill.js';
import Payment from '../models/Payment.js';
import InsuranceClaim from '../models/InsuranceClaim.js';
import FinancialReport from '../models/FinancialReport.js';
import FinancialAnalytics from '../models/FinancialAnalytics.js';
import PDFDocument from 'pdfkit';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { ProfessionalBillGenerator } from '../utils/pdfGenerator.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);


// @desc    Get all bills with pagination and filtering
// @route   GET /api/financial/bills
// @access  Private
export const getBills = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.paymentStatus = req.query.status;
    }
    
    if (req.query.patient) {
      filter.patient = req.query.patient;
    }
    
    if (req.query.dateFrom || req.query.dateTo) {
      filter.billDate = {};
      if (req.query.dateFrom) {
        filter.billDate.$gte = new Date(req.query.dateFrom);
      }
      if (req.query.dateTo) {
        filter.billDate.$lte = new Date(req.query.dateTo);
      }
    }

    const bills = await Bill.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName')
      .populate('payments.processedBy', 'firstName lastName')
      .sort({ billDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Bill.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: bills,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get bills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching bills'
    });
  }
};

// @desc    Get single bill
// @route   GET /api/financial/bills/:id
// @access  Private
export const getBill = async (req, res) => {
  try {
    const bill = await Bill.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId phone email address')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime')
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('payments.receivedBy', 'firstName lastName');

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    res.status(200).json({
      success: true,
      data: bill
    });
  } catch (error) {
    console.error('Get bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching bill'
    });
  }
};

// @desc    Create new bill
// @route   POST /api/financial/bills
// @access  Private
export const createBill = async (req, res) => {
  try {
    const {
      patient,
      appointment,
      items,
      subtotal,
      tax = 0,
      discount = 0,
      totalAmount,
      notes,
      dueDate
    } = req.body;

    // Validation
    if (!patient || !items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Patient and items are required'
      });
    }

    // Process items and calculate required fields
    const processedItems = items.map(item => ({
      ...item,
      taxAmount: item.taxAmount || 0,
      discount: item.discount || 0,
      netAmount: item.totalPrice + (item.taxAmount || 0) - (item.discount || 0)
    }));

    // Calculate totals if not provided
    const calculatedSubtotal = subtotal || processedItems.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
    const calculatedTotal = totalAmount || (calculatedSubtotal + tax - discount);

    // Generate bill ID
    const billId = `BILL${Date.now().toString().slice(-6)}`;

    // Set due date (30 days from now if not provided)
    const finalDueDate = dueDate ? new Date(dueDate) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

    const billData = {
      billId,
      patient,
      appointment,
      items: processedItems,
      subtotal: calculatedSubtotal,
      taxAmount: tax,
      discountAmount: discount,
      totalAmount: calculatedTotal,
      paidAmount: 0,
      balanceAmount: calculatedTotal, // Will be recalculated by pre-save hook
      dueDate: finalDueDate,
      notes,
      createdBy: req.user._id
    };

    const bill = await Bill.create(billData);

    const populatedBill = await Bill.findById(bill._id)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedBill
    });
  } catch (error) {
    console.error('Create bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating bill'
    });
  }
};

// @desc    Update bill
// @route   PUT /api/financial/bills/:id
// @access  Private
export const updateBill = async (req, res) => {
  try {
    const bill = await Bill.findById(req.params.id);

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    req.body.updatedBy = req.user._id;

    const updatedBill = await Bill.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName')
      .populate('payments.receivedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedBill
    });
  } catch (error) {
    console.error('Update bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating bill'
    });
  }
};

// @desc    Add payment to bill
// @route   POST /api/financial/bills/:id/payments
// @access  Private
export const addPayment = async (req, res) => {
  try {
    const bill = await Bill.findById(req.params.id);

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    const payment = {
      ...req.body,
      receivedBy: req.user._id,
      date: new Date()
    };

    bill.payments.push(payment);
    bill.paidAmount += payment.amount;
    bill.updatedBy = req.user._id;

    await bill.save();

    const updatedBill = await Bill.findById(bill._id)
      .populate('patient', 'firstName lastName patientId')
      .populate('payments.receivedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedBill
    });
  } catch (error) {
    console.error('Add payment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while adding payment'
    });
  }
};

// @desc    Get financial statistics
// @route   GET /api/financial/stats
// @access  Private
// @desc    Get comprehensive financial statistics and KPIs
// @route   GET /api/financial/stats
// @access  Private
export const getFinancialStats = async (req, res) => {
  try {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfYear = new Date(today.getFullYear(), 0, 1);
    const startOfQuarter = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);

    // Current month stats
    const monthlyStats = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startOfMonth },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          totalOutstanding: { $sum: '$balanceAmount' },
          billCount: { $sum: 1 },
          avgBillAmount: { $avg: '$totalAmount' }
        }
      }
    ]);

    // Last month stats for comparison
    const lastMonthStats = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: lastMonth, $lte: endOfLastMonth },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          billCount: { $sum: 1 }
        }
      }
    ]);

    // Quarterly stats
    const quarterlyStats = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startOfQuarter },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          totalOutstanding: { $sum: '$balanceAmount' },
          billCount: { $sum: 1 }
        }
      }
    ]);

    // Yearly stats
    const yearlyStats = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startOfYear },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          totalOutstanding: { $sum: '$balanceAmount' },
          billCount: { $sum: 1 }
        }
      }
    ]);

    // Payment method breakdown
    const paymentMethodStats = await Payment.aggregate([
      {
        $match: {
          paymentDate: { $gte: startOfMonth },
          paymentStatus: 'Completed',
          isActive: true
        }
      },
      {
        $group: {
          _id: '$paymentMethod',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { totalAmount: -1 } }
    ]);

    // Department revenue breakdown
    const departmentStats = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startOfMonth },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: '$department',
          totalRevenue: { $sum: '$totalAmount' },
          billCount: { $sum: 1 }
        }
      },
      { $sort: { totalRevenue: -1 } }
    ]);

    // Overdue bills count and amount
    const overdueBills = await Bill.aggregate([
      {
        $match: {
          dueDate: { $lt: today },
          paymentStatus: { $in: ['Pending', 'Partial', 'Overdue'] },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: null,
          count: { $sum: 1 },
          totalAmount: { $sum: '$balanceAmount' }
        }
      }
    ]);

    // Calculate growth rates
    const currentMonth = monthlyStats[0] || { totalRevenue: 0, totalPaid: 0, billCount: 0 };
    const previousMonth = lastMonthStats[0] || { totalRevenue: 0, totalPaid: 0, billCount: 0 };

    const revenueGrowth = previousMonth.totalRevenue > 0
      ? ((currentMonth.totalRevenue - previousMonth.totalRevenue) / previousMonth.totalRevenue) * 100
      : 0;

    const collectionGrowth = previousMonth.totalPaid > 0
      ? ((currentMonth.totalPaid - previousMonth.totalPaid) / previousMonth.totalPaid) * 100
      : 0;

    // Collection rate
    const collectionRate = currentMonth.totalRevenue > 0
      ? (currentMonth.totalPaid / currentMonth.totalRevenue) * 100
      : 0;

    res.status(200).json({
      success: true,
      data: {
        overview: {
          monthly: currentMonth,
          quarterly: quarterlyStats[0] || { totalRevenue: 0, totalPaid: 0, totalOutstanding: 0, billCount: 0 },
          yearly: yearlyStats[0] || { totalRevenue: 0, totalPaid: 0, totalOutstanding: 0, billCount: 0 },
          overdue: overdueBills[0] || { count: 0, totalAmount: 0 }
        },
        kpis: {
          revenueGrowth: Math.round(revenueGrowth * 100) / 100,
          collectionGrowth: Math.round(collectionGrowth * 100) / 100,
          collectionRate: Math.round(collectionRate * 100) / 100,
          avgBillAmount: Math.round((currentMonth.avgBillAmount || 0) * 100) / 100
        },
        breakdowns: {
          paymentMethods: paymentMethodStats,
          departments: departmentStats
        }
      }
    });
  } catch (error) {
    console.error('Get financial stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching financial stats'
    });
  }
};

// @desc    Get payment trends
// @route   GET /api/financial/payment-trends
// @access  Private
export const getPaymentTrends = async (req, res) => {
  try {
    const period = req.query.period || '30'; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const trends = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startDate },
          paymentStatus: { $ne: 'Cancelled' }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$billDate" }
          },
          totalAmount: { $sum: "$totalAmount" },
          paidAmount: { $sum: "$paidAmount" },
          billCount: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: trends
    });
  } catch (error) {
    console.error('Get payment trends error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching payment trends'
    });
  }
};

// @desc    Get overdue bills
// @route   GET /api/financial/overdue-bills
// @access  Private
export const getOverdueBills = async (req, res) => {
  try {
    const overdueBills = await Bill.find({
      paymentStatus: 'Overdue'
    })
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .sort({ dueDate: 1 })
      .limit(50);

    res.status(200).json({
      success: true,
      data: overdueBills
    });
  } catch (error) {
    console.error('Get overdue bills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching overdue bills'
    });
  }
};



// @desc    Get insurance claims
// @route   GET /api/financial/insurance-claims
// @access  Private
export const getInsuranceClaims = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = {
      'insurance.provider': { $exists: true, $ne: null }
    };

    if (req.query.status) {
      filter['insurance.status'] = req.query.status;
    }

    const claims = await Bill.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('appointment', 'appointmentId appointmentDate')
      .sort({ billDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Bill.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: claims,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get insurance claims error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching insurance claims'
    });
  }
};

// @desc    Get advanced revenue analytics with forecasting
// @route   GET /api/financial/revenue-analytics
// @access  Private
export const getRevenueAnalytics = async (req, res) => {
  try {
    const period = parseInt(req.query.period) || 12; // months
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - period);

    // Monthly revenue trend
    const monthlyTrend = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startDate },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$billDate' },
            month: { $month: '$billDate' }
          },
          revenue: { $sum: '$totalAmount' },
          collected: { $sum: '$paidAmount' },
          outstanding: { $sum: '$balanceAmount' },
          billCount: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      },
      {
        $project: {
          _id: 0,
          period: {
            $concat: [
              { $toString: '$_id.year' },
              '-',
              { $cond: [
                { $lt: ['$_id.month', 10] },
                { $concat: ['0', { $toString: '$_id.month' }] },
                { $toString: '$_id.month' }
              ]}
            ]
          },
          revenue: 1,
          collected: 1,
          outstanding: 1,
          billCount: 1,
          collectionRate: {
            $cond: [
              { $eq: ['$revenue', 0] },
              0,
              { $multiply: [{ $divide: ['$collected', '$revenue'] }, 100] }
            ]
          }
        }
      }
    ]);

    // Department-wise revenue analysis
    const departmentAnalysis = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startDate },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: '$department',
          totalRevenue: { $sum: '$totalAmount' },
          totalCollected: { $sum: '$paidAmount' },
          billCount: { $sum: 1 },
          avgBillAmount: { $avg: '$totalAmount' }
        }
      },
      {
        $project: {
          department: '$_id',
          totalRevenue: 1,
          totalCollected: 1,
          billCount: 1,
          avgBillAmount: { $round: ['$avgBillAmount', 2] },
          collectionRate: {
            $cond: [
              { $eq: ['$totalRevenue', 0] },
              0,
              { $round: [{ $multiply: [{ $divide: ['$totalCollected', '$totalRevenue'] }, 100] }, 2] }
            ]
          }
        }
      },
      { $sort: { totalRevenue: -1 } }
    ]);

    // Service category analysis
    const categoryAnalysis = await Bill.aggregate([
      { $unwind: '$items' },
      {
        $match: {
          billDate: { $gte: startDate },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: '$items.category',
          totalRevenue: { $sum: '$items.totalPrice' },
          quantity: { $sum: '$items.quantity' },
          avgPrice: { $avg: '$items.unitPrice' }
        }
      },
      {
        $project: {
          category: '$_id',
          totalRevenue: 1,
          quantity: 1,
          avgPrice: { $round: ['$avgPrice', 2] }
        }
      },
      { $sort: { totalRevenue: -1 } }
    ]);

    // Calculate total for percentage calculations
    const totalRevenue = departmentAnalysis.reduce((sum, dept) => sum + dept.totalRevenue, 0);
    const departmentPercentages = departmentAnalysis.map(dept => ({
      ...dept,
      percentage: totalRevenue > 0 ? Math.round((dept.totalRevenue / totalRevenue) * 100 * 100) / 100 : 0
    }));

    const totalCategoryRevenue = categoryAnalysis.reduce((sum, cat) => sum + cat.totalRevenue, 0);
    const categoryPercentages = categoryAnalysis.map(cat => ({
      ...cat,
      percentage: totalCategoryRevenue > 0 ? Math.round((cat.totalRevenue / totalCategoryRevenue) * 100 * 100) / 100 : 0
    }));

    // Simple forecasting based on trend
    const forecast = [];
    if (monthlyTrend.length >= 3) {
      const recentTrends = monthlyTrend.slice(-3);
      const avgGrowth = recentTrends.reduce((sum, trend, index) => {
        if (index === 0) return 0;
        const prevRevenue = recentTrends[index - 1].revenue;
        return sum + (prevRevenue > 0 ? (trend.revenue - prevRevenue) / prevRevenue : 0);
      }, 0) / (recentTrends.length - 1);

      const lastRevenue = monthlyTrend[monthlyTrend.length - 1].revenue;
      for (let i = 1; i <= 3; i++) {
        const forecastDate = new Date();
        forecastDate.setMonth(forecastDate.getMonth() + i);
        forecast.push({
          period: `${forecastDate.getFullYear()}-${String(forecastDate.getMonth() + 1).padStart(2, '0')}`,
          revenue: Math.round(lastRevenue * Math.pow(1 + avgGrowth, i)),
          type: 'forecast'
        });
      }
    }

    res.status(200).json({
      success: true,
      data: {
        trends: monthlyTrend,
        forecast,
        departmentAnalysis: departmentPercentages,
        categoryAnalysis: categoryPercentages,
        summary: {
          totalRevenue,
          totalDepartments: departmentAnalysis.length,
          totalCategories: categoryAnalysis.length,
          avgMonthlyRevenue: monthlyTrend.length > 0
            ? Math.round(monthlyTrend.reduce((sum, trend) => sum + trend.revenue, 0) / monthlyTrend.length)
            : 0
        }
      }
    });
  } catch (error) {
    console.error('Get revenue analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching revenue analytics'
    });
  }
};

// @desc    Generate and download financial report PDF
// @route   POST /api/financial/reports/generate
// @access  Private
export const generateFinancialReport = async (req, res) => {
  try {
    const {
      reportType,
      startDate,
      endDate,
      filters = {},
      includeCharts = true
    } = req.body;

    // Validate required fields
    if (!reportType || !startDate || !endDate) {
      return res.status(400).json({
        success: false,
        error: 'Report type, start date, and end date are required'
      });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Build query based on filters
    const query = {
      billDate: { $gte: start, $lte: end },
      isActive: true,
      isVoided: false
    };

    if (filters.departments && filters.departments.length > 0) {
      query.department = { $in: filters.departments };
    }

    if (filters.paymentStatus && filters.paymentStatus.length > 0) {
      query.paymentStatus = { $in: filters.paymentStatus };
    }

    if (filters.billTypes && filters.billTypes.length > 0) {
      query.billType = { $in: filters.billTypes };
    }

    // Generate report data based on type
    let reportData = {};

    switch (reportType) {
      case 'Revenue Report':
        reportData = await generateRevenueReportData(query, start, end);
        break;
      case 'Payment Report':
        reportData = await generatePaymentReportData(query, start, end);
        break;
      case 'Outstanding Report':
        reportData = await generateOutstandingReportData(query);
        break;
      case 'Department Revenue':
        reportData = await generateDepartmentReportData(query, start, end);
        break;
      case 'Aging Report':
        reportData = await generateAgingReportData();
        break;
      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid report type'
        });
    }

    // Create PDF
    const pdfBuffer = await createFinancialReportPDF(reportType, reportData, {
      startDate: start,
      endDate: end,
      filters,
      generatedBy: req.user.firstName + ' ' + req.user.lastName,
      generatedAt: new Date()
    });

    // Save report to database
    const report = new FinancialReport({
      reportName: `${reportType} - ${start.toDateString()} to ${end.toDateString()}`,
      reportType,
      reportPeriod: { startDate: start, endDate: end },
      filters,
      reportData,
      generatedBy: req.user._id,
      status: 'Completed'
    });

    await report.save();

    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${reportType.replace(/\s+/g, '_')}_${start.toISOString().split('T')[0]}_to_${end.toISOString().split('T')[0]}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    res.send(pdfBuffer);

  } catch (error) {
    console.error('Generate financial report error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while generating financial report'
    });
  }
};

// Helper function to generate revenue report data
async function generateRevenueReportData(query, startDate, endDate) {
  const [summary, dailyBreakdown, departmentBreakdown] = await Promise.all([
    // Summary statistics
    Bill.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          totalOutstanding: { $sum: '$balanceAmount' },
          billCount: { $sum: 1 },
          avgBillAmount: { $avg: '$totalAmount' }
        }
      }
    ]),

    // Daily breakdown
    Bill.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$billDate' }
          },
          revenue: { $sum: '$totalAmount' },
          collected: { $sum: '$paidAmount' },
          billCount: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]),

    // Department breakdown
    Bill.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$department',
          revenue: { $sum: '$totalAmount' },
          collected: { $sum: '$paidAmount' },
          billCount: { $sum: 1 }
        }
      },
      { $sort: { revenue: -1 } }
    ])
  ]);

  return {
    summary: summary[0] || { totalRevenue: 0, totalPaid: 0, totalOutstanding: 0, billCount: 0, avgBillAmount: 0 },
    dailyBreakdown,
    departmentBreakdown,
    period: { startDate, endDate }
  };
}

// Helper function to generate payment report data
async function generatePaymentReportData(query, startDate, endDate) {
  const paymentQuery = {
    paymentDate: { $gte: startDate, $lte: endDate },
    paymentStatus: 'Completed',
    isActive: true
  };

  const [summary, methodBreakdown, dailyBreakdown] = await Promise.all([
    // Payment summary
    Payment.aggregate([
      { $match: paymentQuery },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          avgTransactionAmount: { $avg: '$amount' },
          totalProcessingFees: { $sum: '$processingFee' }
        }
      }
    ]),

    // Payment method breakdown
    Payment.aggregate([
      { $match: paymentQuery },
      {
        $group: {
          _id: '$paymentMethod',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 },
          avgAmount: { $avg: '$amount' }
        }
      },
      { $sort: { totalAmount: -1 } }
    ]),

    // Daily payment breakdown
    Payment.aggregate([
      { $match: paymentQuery },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$paymentDate' }
          },
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ])
  ]);

  return {
    summary: summary[0] || { totalAmount: 0, totalTransactions: 0, avgTransactionAmount: 0, totalProcessingFees: 0 },
    methodBreakdown,
    dailyBreakdown,
    period: { startDate, endDate }
  };
}

// Helper function to generate outstanding report data
async function generateOutstandingReportData(query) {
  const outstandingQuery = {
    ...query,
    paymentStatus: { $in: ['Pending', 'Partial', 'Overdue'] },
    balanceAmount: { $gt: 0 }
  };

  const [summary, agingBreakdown, departmentBreakdown] = await Promise.all([
    // Outstanding summary
    Bill.aggregate([
      { $match: outstandingQuery },
      {
        $group: {
          _id: null,
          totalOutstanding: { $sum: '$balanceAmount' },
          billCount: { $sum: 1 },
          avgOutstanding: { $avg: '$balanceAmount' }
        }
      }
    ]),

    // Aging breakdown
    Bill.aggregate([
      { $match: outstandingQuery },
      {
        $addFields: {
          daysOverdue: {
            $divide: [
              { $subtract: [new Date(), '$dueDate'] },
              1000 * 60 * 60 * 24
            ]
          }
        }
      },
      {
        $addFields: {
          ageGroup: {
            $switch: {
              branches: [
                { case: { $lte: ['$daysOverdue', 0] }, then: 'Current' },
                { case: { $lte: ['$daysOverdue', 30] }, then: '1-30 days' },
                { case: { $lte: ['$daysOverdue', 60] }, then: '31-60 days' },
                { case: { $lte: ['$daysOverdue', 90] }, then: '61-90 days' }
              ],
              default: '90+ days'
            }
          }
        }
      },
      {
        $group: {
          _id: '$ageGroup',
          totalAmount: { $sum: '$balanceAmount' },
          count: { $sum: 1 }
        }
      }
    ]),

    // Department outstanding breakdown
    Bill.aggregate([
      { $match: outstandingQuery },
      {
        $group: {
          _id: '$department',
          totalOutstanding: { $sum: '$balanceAmount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { totalOutstanding: -1 } }
    ])
  ]);

  return {
    summary: summary[0] || { totalOutstanding: 0, billCount: 0, avgOutstanding: 0 },
    agingBreakdown,
    departmentBreakdown
  };
}

// Helper function to generate department report data
async function generateDepartmentReportData(query, startDate, endDate) {
  const departmentStats = await Bill.aggregate([
    { $match: query },
    {
      $group: {
        _id: '$department',
        totalRevenue: { $sum: '$totalAmount' },
        totalCollected: { $sum: '$paidAmount' },
        totalOutstanding: { $sum: '$balanceAmount' },
        billCount: { $sum: 1 },
        avgBillAmount: { $avg: '$totalAmount' }
      }
    },
    {
      $addFields: {
        collectionRate: {
          $cond: [
            { $eq: ['$totalRevenue', 0] },
            0,
            { $multiply: [{ $divide: ['$totalCollected', '$totalRevenue'] }, 100] }
          ]
        }
      }
    },
    { $sort: { totalRevenue: -1 } }
  ]);

  return {
    departmentStats,
    period: { startDate, endDate }
  };
}

// Helper function to generate aging report data
async function generateAgingReportData() {
  const today = new Date();

  const agingData = await Bill.aggregate([
    {
      $match: {
        paymentStatus: { $in: ['Pending', 'Partial', 'Overdue'] },
        balanceAmount: { $gt: 0 },
        isActive: true,
        isVoided: false
      }
    },
    {
      $addFields: {
        daysOverdue: {
          $divide: [
            { $subtract: [today, '$dueDate'] },
            1000 * 60 * 60 * 24
          ]
        }
      }
    },
    {
      $addFields: {
        ageGroup: {
          $switch: {
            branches: [
              { case: { $lte: ['$daysOverdue', 0] }, then: 'Current' },
              { case: { $lte: ['$daysOverdue', 30] }, then: '1-30 days' },
              { case: { $lte: ['$daysOverdue', 60] }, then: '31-60 days' },
              { case: { $lte: ['$daysOverdue', 90] }, then: '61-90 days' }
            ],
            default: '90+ days'
          }
        }
      }
    },
    {
      $group: {
        _id: '$ageGroup',
        totalAmount: { $sum: '$balanceAmount' },
        count: { $sum: 1 },
        bills: {
          $push: {
            billId: '$billId',
            patient: '$patient',
            amount: '$balanceAmount',
            dueDate: '$dueDate',
            daysOverdue: { $round: ['$daysOverdue', 0] }
          }
        }
      }
    },
    { $sort: { '_id': 1 } }
  ]);

  return { agingData };
}

// Helper function to create PDF report
async function createFinancialReportPDF(reportType, reportData, metadata) {
  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ margin: 50 });
      const chunks = [];

      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));

      // Header
      doc.fontSize(20).text('Hospital Management System', { align: 'center' });
      doc.fontSize(16).text(reportType, { align: 'center' });
      doc.moveDown();

      // Report metadata
      doc.fontSize(12);
      doc.text(`Report Period: ${metadata.startDate.toDateString()} to ${metadata.endDate.toDateString()}`);
      doc.text(`Generated By: ${metadata.generatedBy}`);
      doc.text(`Generated On: ${metadata.generatedAt.toDateString()}`);
      doc.moveDown();

      // Report content based on type
      switch (reportType) {
        case 'Revenue Report':
          addRevenueReportContent(doc, reportData);
          break;
        case 'Payment Report':
          addPaymentReportContent(doc, reportData);
          break;
        case 'Outstanding Report':
          addOutstandingReportContent(doc, reportData);
          break;
        case 'Department Revenue':
          addDepartmentReportContent(doc, reportData);
          break;
        case 'Aging Report':
          addAgingReportContent(doc, reportData);
          break;
      }

      // Footer
      doc.fontSize(10).text('This is a computer-generated report.', { align: 'center' });

      doc.end();
    } catch (error) {
      reject(error);
    }
  });
}

// Helper function to add revenue report content to PDF
function addRevenueReportContent(doc, data) {
  const { summary, dailyBreakdown, departmentBreakdown } = data;

  // Summary section
  doc.fontSize(14).text('Revenue Summary', { underline: true });
  doc.fontSize(12);
  doc.text(`Total Revenue: $${summary.totalRevenue.toLocaleString()}`);
  doc.text(`Total Collected: $${summary.totalPaid.toLocaleString()}`);
  doc.text(`Total Outstanding: $${summary.totalOutstanding.toLocaleString()}`);
  doc.text(`Total Bills: ${summary.billCount}`);
  doc.text(`Average Bill Amount: $${summary.avgBillAmount.toFixed(2)}`);
  doc.moveDown();

  // Department breakdown
  if (departmentBreakdown.length > 0) {
    doc.fontSize(14).text('Department Breakdown', { underline: true });
    doc.fontSize(10);

    departmentBreakdown.forEach(dept => {
      doc.text(`${dept._id || 'Unassigned'}: $${dept.revenue.toLocaleString()} (${dept.billCount} bills)`);
    });
    doc.moveDown();
  }

  // Daily breakdown (show first 10 days)
  if (dailyBreakdown.length > 0) {
    doc.fontSize(14).text('Daily Revenue (First 10 Days)', { underline: true });
    doc.fontSize(10);

    dailyBreakdown.slice(0, 10).forEach(day => {
      doc.text(`${day._id}: $${day.revenue.toLocaleString()} (${day.billCount} bills)`);
    });
  }
}

// Helper function to add payment report content to PDF
function addPaymentReportContent(doc, data) {
  const { summary, methodBreakdown, dailyBreakdown } = data;

  // Summary section
  doc.fontSize(14).text('Payment Summary', { underline: true });
  doc.fontSize(12);
  doc.text(`Total Payments: $${summary.totalAmount.toLocaleString()}`);
  doc.text(`Total Transactions: ${summary.totalTransactions}`);
  doc.text(`Average Transaction: $${summary.avgTransactionAmount.toFixed(2)}`);
  doc.text(`Total Processing Fees: $${summary.totalProcessingFees.toLocaleString()}`);
  doc.moveDown();

  // Payment method breakdown
  if (methodBreakdown.length > 0) {
    doc.fontSize(14).text('Payment Method Breakdown', { underline: true });
    doc.fontSize(10);

    methodBreakdown.forEach(method => {
      doc.text(`${method._id}: $${method.totalAmount.toLocaleString()} (${method.count} transactions)`);
    });
    doc.moveDown();
  }
}

// Helper function to add outstanding report content to PDF
function addOutstandingReportContent(doc, data) {
  const { summary, agingBreakdown, departmentBreakdown } = data;

  // Summary section
  doc.fontSize(14).text('Outstanding Summary', { underline: true });
  doc.fontSize(12);
  doc.text(`Total Outstanding: $${summary.totalOutstanding.toLocaleString()}`);
  doc.text(`Outstanding Bills: ${summary.billCount}`);
  doc.text(`Average Outstanding: $${summary.avgOutstanding.toFixed(2)}`);
  doc.moveDown();

  // Aging breakdown
  if (agingBreakdown.length > 0) {
    doc.fontSize(14).text('Aging Analysis', { underline: true });
    doc.fontSize(10);

    agingBreakdown.forEach(age => {
      doc.text(`${age._id}: $${age.totalAmount.toLocaleString()} (${age.count} bills)`);
    });
    doc.moveDown();
  }

  // Department breakdown
  if (departmentBreakdown.length > 0) {
    doc.fontSize(14).text('Department Outstanding', { underline: true });
    doc.fontSize(10);

    departmentBreakdown.forEach(dept => {
      doc.text(`${dept._id || 'Unassigned'}: $${dept.totalOutstanding.toLocaleString()} (${dept.count} bills)`);
    });
  }
}

// Helper function to add department report content to PDF
function addDepartmentReportContent(doc, data) {
  const { departmentStats } = data;

  doc.fontSize(14).text('Department Performance', { underline: true });
  doc.fontSize(10);

  departmentStats.forEach(dept => {
    doc.text(`Department: ${dept._id || 'Unassigned'}`);
    doc.text(`  Revenue: $${dept.totalRevenue.toLocaleString()}`);
    doc.text(`  Collected: $${dept.totalCollected.toLocaleString()}`);
    doc.text(`  Outstanding: $${dept.totalOutstanding.toLocaleString()}`);
    doc.text(`  Collection Rate: ${dept.collectionRate.toFixed(2)}%`);
    doc.text(`  Bills: ${dept.billCount}`);
    doc.moveDown(0.5);
  });
}

// Helper function to add aging report content to PDF
function addAgingReportContent(doc, data) {
  const { agingData } = data;

  doc.fontSize(14).text('Accounts Receivable Aging', { underline: true });
  doc.fontSize(10);

  agingData.forEach(age => {
    doc.text(`${age._id}: $${age.totalAmount.toLocaleString()} (${age.count} bills)`);

    // Show first 5 bills in each age group
    age.bills.slice(0, 5).forEach(bill => {
      doc.text(`  Bill ${bill.billId}: $${bill.amount.toLocaleString()} (${bill.daysOverdue} days overdue)`, { indent: 20 });
    });

    if (age.bills.length > 5) {
      doc.text(`  ... and ${age.bills.length - 5} more bills`, { indent: 20 });
    }
    doc.moveDown(0.5);
  });
}

// @desc    Process payment for a bill
// @route   POST /api/financial/payments
// @access  Private
export const processPayment = async (req, res) => {
  try {
    const {
      billId,
      amount,
      paymentMethod,
      paymentDetails = {},
      notes
    } = req.body;

    // Validate required fields
    if (!billId || !amount || !paymentMethod) {
      return res.status(400).json({
        success: false,
        error: 'Bill ID, amount, and payment method are required'
      });
    }

    // Find the bill
    const bill = await Bill.findById(billId);
    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    // Validate payment amount
    if (amount <= 0 || amount > bill.balanceAmount) {
      return res.status(400).json({
        success: false,
        error: 'Invalid payment amount'
      });
    }

    // Create payment record
    const payment = new Payment({
      bill: billId,
      patient: bill.patient,
      amount,
      paymentMethod,
      paymentDetails,
      notes,
      processedBy: req.user._id,
      paymentStatus: 'Completed',
      netAmount: amount // Calculate net amount (amount - processing fees)
    });

    await payment.save();

    // Update bill with payment
    bill.payments.push({
      amount,
      paymentMethod,
      paymentDate: new Date(),
      transactionId: payment.transactionId,
      processedBy: req.user._id,
      notes
    });

    bill.paidAmount += amount;
    bill.balanceAmount = bill.totalAmount - bill.paidAmount;

    // Update payment status
    if (bill.paidAmount >= bill.totalAmount) {
      bill.paymentStatus = 'Paid';
    } else if (bill.paidAmount > 0) {
      bill.paymentStatus = 'Partial';
    }

    await bill.save();

    // Populate the payment for response
    const populatedPayment = await Payment.findById(payment._id)
      .populate('bill', 'billId billNumber totalAmount')
      .populate('patient', 'firstName lastName patientId')
      .populate('processedBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedPayment
    });

  } catch (error) {
    console.error('Process payment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while processing payment'
    });
  }
};

// @desc    Get payment history for a patient or bill
// @route   GET /api/financial/payments
// @access  Private
export const getPayments = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter
    const filter = { isActive: true };

    if (req.query.patient) {
      filter.patient = req.query.patient;
    }

    if (req.query.bill) {
      filter.bill = req.query.bill;
    }

    if (req.query.paymentMethod) {
      filter.paymentMethod = req.query.paymentMethod;
    }

    if (req.query.status) {
      filter.paymentStatus = req.query.status;
    }

    if (req.query.dateFrom || req.query.dateTo) {
      filter.paymentDate = {};
      if (req.query.dateFrom) {
        filter.paymentDate.$gte = new Date(req.query.dateFrom);
      }
      if (req.query.dateTo) {
        filter.paymentDate.$lte = new Date(req.query.dateTo);
      }
    }

    const payments = await Payment.find(filter)
      .populate('bill', 'billId billNumber totalAmount')
      .populate('patient', 'firstName lastName patientId phone')
      .populate('processedBy', 'firstName lastName')
      .sort({ paymentDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Payment.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: payments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get payments error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching payments'
    });
  }
};

// @desc    Process refund for a payment
// @route   POST /api/financial/payments/:id/refund
// @access  Private
export const processRefund = async (req, res) => {
  try {
    const { amount, reason, refundMethod = 'Original Payment Method' } = req.body;

    if (!amount || !reason) {
      return res.status(400).json({
        success: false,
        error: 'Refund amount and reason are required'
      });
    }

    const payment = await Payment.findById(req.params.id);
    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }

    if (!payment.canRefund()) {
      return res.status(400).json({
        success: false,
        error: 'Payment cannot be refunded'
      });
    }

    if (amount > (payment.amount - payment.getTotalRefunded())) {
      return res.status(400).json({
        success: false,
        error: 'Refund amount exceeds available amount'
      });
    }

    // Add refund to payment
    await payment.addRefund({
      amount,
      reason,
      refundMethod,
      refundStatus: 'Completed',
      processedBy: req.user._id,
      approvedBy: req.user._id
    });

    // Update the associated bill
    const bill = await Bill.findById(payment.bill);
    if (bill) {
      bill.paidAmount -= amount;
      bill.balanceAmount += amount;

      // Update payment status if needed
      if (bill.paidAmount === 0) {
        bill.paymentStatus = 'Pending';
      } else if (bill.paidAmount < bill.totalAmount) {
        bill.paymentStatus = 'Partial';
      }

      await bill.save();
    }

    const updatedPayment = await Payment.findById(payment._id)
      .populate('bill', 'billId billNumber')
      .populate('patient', 'firstName lastName patientId')
      .populate('processedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedPayment
    });

  } catch (error) {
    console.error('Process refund error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while processing refund'
    });
  }
};

// @desc    Get payment history for a patient
// @route   GET /api/financial/patients/:patientId/payments
// @access  Private
export const getPatientPaymentHistory = async (req, res) => {
  try {
    const { patientId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const skip = (page - 1) * limit;

    const payments = await Payment.find({ patient: patientId })
      .populate('bill', 'billId billNumber totalAmount')
      .populate('processedBy', 'firstName lastName')
      .sort({ paymentDate: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Payment.countDocuments({ patient: patientId });

    res.status(200).json({
      success: true,
      data: payments,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Get patient payment history error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching payment history'
    });
  }
};

// @desc    Generate professional bill PDF
// @route   GET /api/financial/bills/:id/pdf
// @access  Private
export const generateBillPDF = async (req, res) => {
  try {
    const { id } = req.params;

    const bill = await Bill.findById(id)
      .populate('patient', 'firstName lastName patientId phone email address')
      .populate('vendor')
      .populate('employee', 'firstName lastName employeeId department email')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('createdBy', 'firstName lastName');

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    // Generate professional PDF using the new generator
    const pdfGenerator = new ProfessionalBillGenerator();
    const pdfBuffer = await pdfGenerator.generateBill(bill, bill.billType);

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${bill.billType.replace(/\s+/g, '_')}-${bill.billNumber}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    res.send(pdfBuffer);

  } catch (error) {
    console.error('Generate bill PDF error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while generating PDF'
    });
  }
};

// @desc    Create payment plan
// @route   POST /api/financial/bills/:id/payment-plan
// @access  Private
export const createPaymentPlan = async (req, res) => {
  try {
    const { id } = req.params;
    const { installments, startDate, frequency } = req.body;

    const bill = await Bill.findById(id);
    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    if (bill.balanceAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Bill is already fully paid'
      });
    }

    const installmentAmount = Math.ceil(bill.balanceAmount / installments);
    const plan = [];
    let currentDate = new Date(startDate);

    for (let i = 0; i < installments; i++) {
      const isLast = i === installments - 1;
      const amount = isLast ? bill.balanceAmount - (installmentAmount * (installments - 1)) : installmentAmount;

      plan.push({
        installmentNumber: i + 1,
        amount,
        dueDate: new Date(currentDate),
        status: 'Pending'
      });

      // Calculate next due date based on frequency
      switch (frequency) {
        case 'weekly':
          currentDate.setDate(currentDate.getDate() + 7);
          break;
        case 'biweekly':
          currentDate.setDate(currentDate.getDate() + 14);
          break;
        case 'monthly':
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        default:
          currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }

    // Update bill with payment plan
    bill.paymentPlan = {
      isActive: true,
      installments: plan,
      frequency,
      totalInstallments: installments,
      paidInstallments: 0
    };

    await bill.save();

    res.status(200).json({
      success: true,
      data: bill.paymentPlan
    });
  } catch (error) {
    console.error('Create payment plan error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating payment plan'
    });
  }
};

// @desc    Generate comprehensive financial analytics
// @route   POST /api/financial/analytics/generate
// @access  Private
export const generateComprehensiveAnalytics = async (req, res) => {
  try {
    const { period = 'monthly', startDate, endDate } = req.body;

    const start = startDate ? new Date(startDate) : getDefaultStartDate(period);
    const end = endDate ? new Date(endDate) : new Date();

    // Generate analytics data
    const analyticsData = await generateAnalyticsData(period, start, end, req.user._id);

    // Save analytics to database
    const analytics = new FinancialAnalytics(analyticsData);
    await analytics.save();

    res.status(201).json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Generate comprehensive analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while generating analytics'
    });
  }
};

// @desc    Get comprehensive financial dashboard data
// @route   GET /api/financial/dashboard
// @access  Private
export const getFinancialDashboard = async (req, res) => {
  try {
    const { period = 'monthly' } = req.query;

    // Get latest analytics
    const latestAnalytics = await FinancialAnalytics.getLatestAnalytics(period);

    // Get real-time data
    const realTimeData = await getRealTimeFinancialData();

    // Get trend data
    const trendData = await FinancialAnalytics.getTrendData(period, 12);

    // Get alerts and notifications
    const alerts = await getFinancialAlerts();

    res.status(200).json({
      success: true,
      data: {
        analytics: latestAnalytics,
        realTime: realTimeData,
        trends: trendData,
        alerts
      }
    });

  } catch (error) {
    console.error('Get financial dashboard error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching dashboard data'
    });
  }
};

// @desc    Create bill for any type (patient, vendor, employee, etc.)
// @route   POST /api/financial/bills/universal
// @access  Private
export const createUniversalBill = async (req, res) => {
  try {
    const {
      billType,
      patient,
      vendor,
      employee,
      items,
      subtotal,
      tax = 0,
      discount = 0,
      totalAmount,
      notes,
      dueDate,
      costCenter,
      budgetCode,
      projectCode,
      poNumber,
      invoiceNumber
    } = req.body;

    // Validation based on bill type
    if (billType === 'Patient Bill' && !patient) {
      return res.status(400).json({
        success: false,
        error: 'Patient is required for patient bills'
      });
    }

    if (['Vendor Bill', 'Supplier Bill', 'Utility Bill', 'Service Bill'].includes(billType) && !vendor) {
      return res.status(400).json({
        success: false,
        error: 'Vendor information is required for vendor bills'
      });
    }

    if (['Employee Expense', 'Payroll', 'Reimbursement'].includes(billType) && !employee) {
      return res.status(400).json({
        success: false,
        error: 'Employee is required for employee-related bills'
      });
    }

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Items are required'
      });
    }

    // Process items and calculate required fields
    const processedItems = items.map(item => ({
      ...item,
      taxAmount: item.taxAmount || 0,
      discount: item.discount || 0,
      netAmount: item.totalPrice + (item.taxAmount || 0) - (item.discount || 0)
    }));

    // Calculate totals if not provided
    const calculatedSubtotal = subtotal || processedItems.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
    const calculatedTotal = totalAmount || (calculatedSubtotal + tax - discount);

    // Generate bill ID based on type
    const billId = generateBillId(billType);

    // Set due date based on bill type
    const finalDueDate = dueDate ? new Date(dueDate) : getDefaultDueDate(billType);

    const billData = {
      billId,
      billType,
      patient,
      vendor,
      employee,
      items: processedItems,
      subtotal: calculatedSubtotal,
      taxAmount: tax,
      discountAmount: discount,
      totalAmount: calculatedTotal,
      paidAmount: 0,
      balanceAmount: calculatedTotal,
      dueDate: finalDueDate,
      notes,
      costCenter,
      budgetCode,
      projectCode,
      poNumber,
      invoiceNumber,
      createdBy: req.user._id
    };

    const bill = await Bill.create(billData);

    // Populate based on bill type
    let populateFields = ['createdBy'];
    if (patient) populateFields.push('patient');
    if (employee) populateFields.push('employee');

    const populatedBill = await Bill.findById(bill._id).populate(populateFields.join(' '));

    res.status(201).json({
      success: true,
      data: populatedBill
    });
  } catch (error) {
    console.error('Create universal bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating bill'
    });
  }
};

// Helper Functions

// Generate bill ID based on type
function generateBillId(billType) {
  const typePrefix = {
    'Patient Bill': 'PB',
    'Emergency Bill': 'EB',
    'Insurance Bill': 'IB',
    'Vendor Bill': 'VB',
    'Supplier Bill': 'SB',
    'Utility Bill': 'UB',
    'Service Bill': 'SVB',
    'Employee Expense': 'EE',
    'Payroll': 'PR',
    'Reimbursement': 'RB'
  };

  const prefix = typePrefix[billType] || 'BILL';
  return `${prefix}${Date.now().toString().slice(-6)}`;
}

// Get default due date based on bill type
function getDefaultDueDate(billType) {
  const daysMap = {
    'Patient Bill': 30,
    'Emergency Bill': 15,
    'Insurance Bill': 45,
    'Vendor Bill': 30,
    'Supplier Bill': 30,
    'Utility Bill': 15,
    'Service Bill': 30,
    'Employee Expense': 7,
    'Payroll': 0,
    'Reimbursement': 7
  };

  const days = daysMap[billType] || 30;
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date;
}

// Get default start date based on period
function getDefaultStartDate(period) {
  const date = new Date();
  switch (period) {
    case 'daily':
      return new Date(date.setDate(date.getDate() - 1));
    case 'weekly':
      return new Date(date.setDate(date.getDate() - 7));
    case 'monthly':
      return new Date(date.setMonth(date.getMonth() - 1));
    case 'quarterly':
      return new Date(date.setMonth(date.getMonth() - 3));
    case 'yearly':
      return new Date(date.setFullYear(date.getFullYear() - 1));
    default:
      return new Date(date.setMonth(date.getMonth() - 1));
  }
}

// Get real-time financial data
async function getRealTimeFinancialData() {
  const today = new Date();
  const startOfDay = new Date(today.setHours(0, 0, 0, 0));
  const endOfDay = new Date(today.setHours(23, 59, 59, 999));

  const [todayStats, weekStats, monthStats] = await Promise.all([
    // Today's stats
    Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startOfDay, $lte: endOfDay },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          billCount: { $sum: 1 }
        }
      }
    ]),

    // This week's stats
    Bill.aggregate([
      {
        $match: {
          billDate: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          billCount: { $sum: 1 }
        }
      }
    ]),

    // This month's stats
    Bill.aggregate([
      {
        $match: {
          billDate: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalPaid: { $sum: '$paidAmount' },
          billCount: { $sum: 1 }
        }
      }
    ])
  ]);

  return {
    today: todayStats[0] || { totalRevenue: 0, totalPaid: 0, billCount: 0 },
    week: weekStats[0] || { totalRevenue: 0, totalPaid: 0, billCount: 0 },
    month: monthStats[0] || { totalRevenue: 0, totalPaid: 0, billCount: 0 }
  };
}

// Get financial alerts
async function getFinancialAlerts() {
  const today = new Date();

  const [overdueBills, lowCashFlow, highExpenses] = await Promise.all([
    // Overdue bills
    Bill.countDocuments({
      dueDate: { $lt: today },
      paymentStatus: { $in: ['Pending', 'Partial', 'Overdue'] },
      isActive: true,
      isVoided: false
    }),

    // Low cash flow (placeholder - would need actual cash flow calculation)
    Promise.resolve(0),

    // High expenses (placeholder - would need expense threshold calculation)
    Promise.resolve(0)
  ]);

  const alerts = [];

  if (overdueBills > 0) {
    alerts.push({
      type: 'warning',
      title: 'Overdue Bills',
      message: `${overdueBills} bills are overdue`,
      count: overdueBills
    });
  }

  return alerts;
}

// Generate comprehensive analytics data
async function generateAnalyticsData(period, startDate, endDate, userId) {
  const analyticsData = {
    analyticsId: 'FA' + Date.now().toString().slice(-8),
    period,
    startDate,
    endDate,
    generatedBy: userId,
    status: 'generating'
  };

  try {
    // Revenue Analytics
    const revenueData = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startDate, $lte: endDate },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$totalAmount' },
          paid: { $sum: '$paidAmount' },
          outstanding: { $sum: '$balanceAmount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Department-wise revenue
    const departmentRevenue = await Bill.aggregate([
      {
        $match: {
          billDate: { $gte: startDate, $lte: endDate },
          isActive: true,
          isVoided: false
        }
      },
      {
        $group: {
          _id: '$department',
          amount: { $sum: '$totalAmount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { amount: -1 } }
    ]);

    // Calculate percentages for department revenue
    const totalRevenue = revenueData[0]?.total || 0;
    const departmentWithPercentages = departmentRevenue.map(dept => ({
      department: dept._id || 'Unassigned',
      amount: dept.amount,
      percentage: totalRevenue > 0 ? (dept.amount / totalRevenue) * 100 : 0
    }));

    // Payment method analytics
    const paymentMethodData = await Bill.aggregate([
      { $unwind: '$payments' },
      {
        $match: {
          'payments.paymentDate': { $gte: startDate, $lte: endDate },
          'payments.paymentStatus': 'Completed'
        }
      },
      {
        $group: {
          _id: '$payments.paymentMethod',
          amount: { $sum: '$payments.amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { amount: -1 } }
    ]);

    const paymentMethodWithPercentages = paymentMethodData.map(method => ({
      method: method._id,
      amount: method.amount,
      percentage: totalRevenue > 0 ? (method.amount / totalRevenue) * 100 : 0
    }));

    // KPIs calculation
    const kpis = [
      {
        name: 'Total Revenue',
        value: totalRevenue,
        unit: 'currency',
        category: 'revenue'
      },
      {
        name: 'Collection Rate',
        value: totalRevenue > 0 ? ((revenueData[0]?.paid || 0) / totalRevenue) * 100 : 0,
        unit: 'percentage',
        category: 'efficiency'
      },
      {
        name: 'Outstanding Amount',
        value: revenueData[0]?.outstanding || 0,
        unit: 'currency',
        category: 'liquidity'
      },
      {
        name: 'Average Bill Amount',
        value: (revenueData[0]?.count || 0) > 0 ? totalRevenue / revenueData[0].count : 0,
        unit: 'currency',
        category: 'activity'
      }
    ];

    // Accounts Receivable Analytics
    const arData = await Bill.aggregate([
      {
        $match: {
          paymentStatus: { $in: ['Pending', 'Partial', 'Overdue'] },
          balanceAmount: { $gt: 0 },
          isActive: true,
          isVoided: false
        }
      },
      {
        $addFields: {
          daysOverdue: {
            $divide: [
              { $subtract: [new Date(), '$dueDate'] },
              1000 * 60 * 60 * 24
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$balanceAmount' },
          current: {
            $sum: {
              $cond: [{ $lte: ['$daysOverdue', 0] }, '$balanceAmount', 0]
            }
          },
          overdue30: {
            $sum: {
              $cond: [
                { $and: [{ $gt: ['$daysOverdue', 0] }, { $lte: ['$daysOverdue', 30] }] },
                '$balanceAmount',
                0
              ]
            }
          },
          overdue60: {
            $sum: {
              $cond: [
                { $and: [{ $gt: ['$daysOverdue', 30] }, { $lte: ['$daysOverdue', 60] }] },
                '$balanceAmount',
                0
              ]
            }
          },
          overdue90: {
            $sum: {
              $cond: [
                { $and: [{ $gt: ['$daysOverdue', 60] }, { $lte: ['$daysOverdue', 90] }] },
                '$balanceAmount',
                0
              ]
            }
          },
          overdue90Plus: {
            $sum: {
              $cond: [{ $gt: ['$daysOverdue', 90] }, '$balanceAmount', 0]
            }
          }
        }
      }
    ]);

    // Populate analytics data
    analyticsData.revenue = {
      total: totalRevenue,
      byDepartment: departmentWithPercentages,
      byPaymentMethod: paymentMethodWithPercentages
    };

    analyticsData.accountsReceivable = arData[0] || {
      total: 0,
      current: 0,
      overdue30: 0,
      overdue60: 0,
      overdue90: 0,
      overdue90Plus: 0
    };

    analyticsData.kpis = kpis;
    analyticsData.status = 'completed';

    return analyticsData;

  } catch (error) {
    console.error('Error generating analytics data:', error);
    analyticsData.status = 'error';
    return analyticsData;
  }
}
