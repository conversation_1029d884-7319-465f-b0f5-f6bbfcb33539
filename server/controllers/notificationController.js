import Notification from '../models/Notification.js';

// @desc    Get user notifications
// @route   GET /api/notifications
// @access  Private
export const getNotifications = async (req, res) => {
  try {
    const { page = 1, limit = 20, type, category, isRead } = req.query;
    const userId = req.user.id;

    const query = {
      recipient: userId,
      isActive: true
    };

    // Add filters
    if (type) query.type = type;
    if (category) query.category = category;
    if (isRead !== undefined) query.isRead = isRead === 'true';

    const notifications = await Notification.find(query)
      .populate('sender', 'firstName lastName role')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Notification.countDocuments(query);

    res.status(200).json({
      success: true,
      data: notifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching notifications'
    });
  }
};

// @desc    Get unread notification count
// @route   GET /api/notifications/unread-count
// @access  Private
export const getUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;
    const count = await Notification.getUnreadCount(userId);

    res.status(200).json({
      success: true,
      data: { count }
    });
  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching unread count'
    });
  }
};

// @desc    Mark notification as read
// @route   PUT /api/notifications/:id/read
// @access  Private
export const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const notification = await Notification.findOne({
      _id: id,
      recipient: userId
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }

    await notification.markAsRead();

    res.status(200).json({
      success: true,
      data: notification
    });
  } catch (error) {
    console.error('Mark as read error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while marking notification as read'
    });
  }
};

// @desc    Mark all notifications as read
// @route   PUT /api/notifications/mark-all-read
// @access  Private
export const markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    
    await Notification.markAllAsRead(userId);

    res.status(200).json({
      success: true,
      message: 'All notifications marked as read'
    });
  } catch (error) {
    console.error('Mark all as read error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while marking all notifications as read'
    });
  }
};

// @desc    Create notification (admin only)
// @route   POST /api/notifications
// @access  Private/Admin
export const createNotification = async (req, res) => {
  try {
    const {
      recipient,
      title,
      message,
      type = 'info',
      priority = 'medium',
      category = 'general',
      data = {},
      actionUrl,
      expiresAt
    } = req.body;

    const notificationData = {
      recipient,
      sender: req.user.id,
      title,
      message,
      type,
      priority,
      category,
      data,
      actionUrl,
      expiresAt
    };

    const notification = await Notification.createNotification(notificationData);

    res.status(201).json({
      success: true,
      data: notification
    });
  } catch (error) {
    console.error('Create notification error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating notification'
    });
  }
};

// @desc    Delete notification
// @route   DELETE /api/notifications/:id
// @access  Private
export const deleteNotification = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const notification = await Notification.findOneAndUpdate(
      { _id: id, recipient: userId },
      { isActive: false },
      { new: true }
    );

    if (!notification) {
      return res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting notification'
    });
  }
};

// @desc    Get notification preferences
// @route   GET /api/notifications/preferences
// @access  Private
export const getNotificationPreferences = async (req, res) => {
  try {
    const userId = req.user.id;

    // For now, return default preferences since we don't have User model import
    // const user = await User.findById(userId).select('notificationPreferences');
    
    const defaultPreferences = {
      email: {
        appointments: true,
        payments: true,
        system: true,
        marketing: false
      },
      push: {
        appointments: true,
        payments: true,
        system: true,
        marketing: false
      },
      sms: {
        appointments: false,
        payments: false,
        system: false,
        marketing: false
      }
    };

    const preferences = defaultPreferences; // user?.notificationPreferences || defaultPreferences;

    res.status(200).json({
      success: true,
      data: preferences
    });
  } catch (error) {
    console.error('Get notification preferences error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching notification preferences'
    });
  }
};

// @desc    Update notification preferences
// @route   PUT /api/notifications/preferences
// @access  Private
export const updateNotificationPreferences = async (req, res) => {
  try {
    const userId = req.user.id;
    const { preferences } = req.body;

    // For now, just return the preferences since we don't have User model import
    // const user = await User.findByIdAndUpdate(
    //   userId,
    //   { notificationPreferences: preferences },
    //   { new: true, runValidators: true }
    // ).select('notificationPreferences');

    res.status(200).json({
      success: true,
      data: preferences
    });
  } catch (error) {
    console.error('Update notification preferences error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating notification preferences'
    });
  }
};

// Helper function to create system notifications
export const createSystemNotification = async (recipientId, title, message, type = 'system', data = {}) => {
  try {
    return await Notification.createNotification({
      recipient: recipientId,
      title,
      message,
      type,
      category: 'system',
      data
    });
  } catch (error) {
    console.error('Create system notification error:', error);
  }
};

// Helper function to create appointment notifications
export const createAppointmentNotification = async (recipientId, title, message, appointmentData) => {
  try {
    return await Notification.createNotification({
      recipient: recipientId,
      title,
      message,
      type: 'appointment',
      category: 'appointment',
      data: appointmentData,
      actionUrl: `/appointments/${appointmentData.appointmentId}`
    });
  } catch (error) {
    console.error('Create appointment notification error:', error);
  }
};
