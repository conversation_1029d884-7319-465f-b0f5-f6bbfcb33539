import mongoose from 'mongoose';

// Room schema
const RoomSchema = new mongoose.Schema({
  roomId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'ROOM' + Date.now().toString().slice(-6);
    }
  },
  roomNumber: {
    type: String,
    required: true,
    unique: true
  },
  roomType: {
    type: String,
    required: true,
    enum: ['Patient Room', 'ICU', 'Operating Room', 'Emergency Room', 'Laboratory', 'Radiology', 'Pharmacy', 'Office', 'Storage', 'Other']
  },
  floor: {
    type: String,
    required: true
  },
  building: {
    type: String,
    required: true
  },
  capacity: {
    type: Number,
    required: true,
    min: 1
  },
  currentOccupancy: {
    type: Number,
    default: 0,
    min: 0
  },
  status: {
    type: String,
    enum: ['Available', 'Occupied', 'Maintenance', 'Cleaning', 'Out of Service'],
    default: 'Available'
  },
  amenities: [String],
  equipment: [{
    name: String,
    quantity: Number,
    status: {
      type: String,
      enum: ['Working', 'Maintenance', 'Out of Order'],
      default: 'Working'
    }
  }],
  dailyRate: {
    type: Number,
    min: 0
  },
  notes: String,
  lastMaintenance: Date,
  nextMaintenance: Date,
  assignedStaff: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Equipment schema
const EquipmentSchema = new mongoose.Schema({
  equipmentId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'EQ' + Date.now().toString().slice(-6);
    }
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['Medical Device', 'Diagnostic Equipment', 'Surgical Instrument', 'IT Equipment', 'Furniture', 'Vehicle', 'Other']
  },
  manufacturer: String,
  model: String,
  serialNumber: String,
  purchaseDate: Date,
  warrantyExpiry: Date,
  location: {
    room: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Room'
    },
    building: String,
    floor: String,
    description: String
  },
  status: {
    type: String,
    enum: ['Available', 'In Use', 'Maintenance', 'Out of Order', 'Retired'],
    default: 'Available'
  },
  condition: {
    type: String,
    enum: ['Excellent', 'Good', 'Fair', 'Poor'],
    default: 'Good'
  },
  purchasePrice: {
    type: Number,
    min: 0
  },
  currentValue: {
    type: Number,
    min: 0
  },
  maintenanceSchedule: {
    frequency: {
      type: String,
      enum: ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annually', 'As Needed']
    },
    lastMaintenance: Date,
    nextMaintenance: Date
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  notes: String,
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Maintenance Request schema
const MaintenanceRequestSchema = new mongoose.Schema({
  requestId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'MAINT' + Date.now().toString().slice(-6);
    }
  },
  type: {
    type: String,
    required: true,
    enum: ['Room', 'Equipment']
  },
  targetId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    refPath: 'type'
  },
  priority: {
    type: String,
    required: true,
    enum: ['Low', 'Medium', 'High', 'Emergency'],
    default: 'Medium'
  },
  category: {
    type: String,
    required: true,
    enum: ['Preventive', 'Corrective', 'Emergency', 'Upgrade', 'Inspection']
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  reportedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  status: {
    type: String,
    enum: ['Open', 'In Progress', 'Completed', 'Cancelled', 'On Hold'],
    default: 'Open'
  },
  scheduledDate: Date,
  completedDate: Date,
  estimatedCost: {
    type: Number,
    min: 0
  },
  actualCost: {
    type: Number,
    min: 0
  },
  workPerformed: String,
  partsUsed: [String],
  notes: String
}, {
  timestamps: true
});

// Indexes
RoomSchema.index({ roomNumber: 1 });
RoomSchema.index({ roomType: 1 });
RoomSchema.index({ status: 1 });
RoomSchema.index({ building: 1, floor: 1 });

EquipmentSchema.index({ equipmentId: 1 });
EquipmentSchema.index({ name: 1 });
EquipmentSchema.index({ category: 1 });
EquipmentSchema.index({ status: 1 });

MaintenanceRequestSchema.index({ requestId: 1 });
MaintenanceRequestSchema.index({ status: 1 });
MaintenanceRequestSchema.index({ priority: 1 });
MaintenanceRequestSchema.index({ scheduledDate: 1 });

// Get existing models or create new ones
let Room, Equipment, MaintenanceRequest;
try {
  Room = mongoose.model('Room');
  Equipment = mongoose.model('Equipment');
  MaintenanceRequest = mongoose.model('MaintenanceRequest');
} catch (error) {
  Room = mongoose.model('Room', RoomSchema);
  Equipment = mongoose.model('Equipment', EquipmentSchema);
  MaintenanceRequest = mongoose.model('MaintenanceRequest', MaintenanceRequestSchema);
}

// @desc    Get all rooms with pagination and filtering
// @route   GET /api/facility/rooms
// @access  Private
export const getRooms = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = { isActive: true };
    
    if (req.query.roomType) {
      filter.roomType = req.query.roomType;
    }
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.building) {
      filter.building = req.query.building;
    }
    
    if (req.query.floor) {
      filter.floor = req.query.floor;
    }

    const rooms = await Room.find(filter)
      .populate('assignedStaff', 'firstName lastName')
      .sort({ building: 1, floor: 1, roomNumber: 1 })
      .skip(skip)
      .limit(limit);

    const total = await Room.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: rooms,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get rooms error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching rooms'
    });
  }
};

// @desc    Create new room
// @route   POST /api/facility/rooms
// @access  Private
export const createRoom = async (req, res) => {
  try {
    const room = await Room.create(req.body);

    const populatedRoom = await Room.findById(room._id)
      .populate('assignedStaff', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedRoom
    });
  } catch (error) {
    console.error('Create room error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating room'
    });
  }
};

// @desc    Get all equipment with pagination and filtering
// @route   GET /api/facility/equipment
// @access  Private
export const getEquipment = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = { isActive: true };
    
    if (req.query.category) {
      filter.category = req.query.category;
    }
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.location) {
      filter['location.room'] = req.query.location;
    }

    const equipment = await Equipment.find(filter)
      .populate('location.room', 'roomNumber building floor')
      .populate('assignedTo', 'firstName lastName')
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit);

    const total = await Equipment.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: equipment,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get equipment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching equipment'
    });
  }
};

// @desc    Create new equipment
// @route   POST /api/facility/equipment
// @access  Private
export const createEquipment = async (req, res) => {
  try {
    const equipment = await Equipment.create(req.body);

    const populatedEquipment = await Equipment.findById(equipment._id)
      .populate('location.room', 'roomNumber building floor')
      .populate('assignedTo', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedEquipment
    });
  } catch (error) {
    console.error('Create equipment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating equipment'
    });
  }
};

// @desc    Get all maintenance requests
// @route   GET /api/facility/maintenance
// @access  Private
export const getMaintenanceRequests = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.priority) {
      filter.priority = req.query.priority;
    }
    
    if (req.query.type) {
      filter.type = req.query.type;
    }

    const requests = await MaintenanceRequest.find(filter)
      .populate('reportedBy', 'firstName lastName')
      .populate('assignedTo', 'firstName lastName')
      .populate({
        path: 'targetId',
        select: 'roomNumber name equipmentId',
        model: function(doc) {
          return doc.type === 'Room' ? 'Room' : 'Equipment';
        }
      })
      .sort({ priority: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await MaintenanceRequest.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: requests,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get maintenance requests error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching maintenance requests'
    });
  }
};

// @desc    Create new maintenance request
// @route   POST /api/facility/maintenance
// @access  Private
export const createMaintenanceRequest = async (req, res) => {
  try {
    req.body.reportedBy = req.user._id;

    const request = await MaintenanceRequest.create(req.body);

    const populatedRequest = await MaintenanceRequest.findById(request._id)
      .populate('reportedBy', 'firstName lastName')
      .populate('assignedTo', 'firstName lastName')
      .populate({
        path: 'targetId',
        select: 'roomNumber name equipmentId',
        model: request.type === 'Room' ? 'Room' : 'Equipment'
      });

    res.status(201).json({
      success: true,
      data: populatedRequest
    });
  } catch (error) {
    console.error('Create maintenance request error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating maintenance request'
    });
  }
};

// @desc    Get facility statistics
// @route   GET /api/facility/stats
// @access  Private
export const getFacilityStats = async (req, res) => {
  try {
    const stats = await Promise.all([
      // Room statistics
      Room.countDocuments({ isActive: true }),
      Room.countDocuments({ status: 'Available', isActive: true }),
      Room.countDocuments({ status: 'Occupied', isActive: true }),
      Room.countDocuments({ status: 'Maintenance', isActive: true }),

      // Equipment statistics
      Equipment.countDocuments({ isActive: true }),
      Equipment.countDocuments({ status: 'Available', isActive: true }),
      Equipment.countDocuments({ status: 'Maintenance', isActive: true }),
      Equipment.countDocuments({ status: 'Out of Order', isActive: true }),

      // Maintenance statistics
      MaintenanceRequest.countDocuments({ status: 'Open' }),
      MaintenanceRequest.countDocuments({ status: 'In Progress' }),
      MaintenanceRequest.countDocuments({ priority: 'Emergency', status: { $in: ['Open', 'In Progress'] } })
    ]);

    res.status(200).json({
      success: true,
      data: {
        rooms: {
          total: stats[0],
          available: stats[1],
          occupied: stats[2],
          maintenance: stats[3]
        },
        equipment: {
          total: stats[4],
          available: stats[5],
          maintenance: stats[6],
          outOfOrder: stats[7]
        },
        maintenance: {
          openRequests: stats[8],
          inProgress: stats[9],
          emergencyRequests: stats[10]
        }
      }
    });
  } catch (error) {
    console.error('Get facility stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching facility statistics'
    });
  }
};
