import mongoose from 'mongoose';
import pdfService from '../services/pdfService.js';

// Inventory Item schema
const InventoryItemSchema = new mongoose.Schema({
  itemId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'INV' + Date.now().toString().slice(-6);
    }
  },
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  description: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['Medication', 'Medical Equipment', 'Surgical Instruments', 'Consumables', 'Laboratory Supplies'],
    index: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  manufacturer: {
    type: String,
    trim: true
  },
  batchNumber: {
    type: String,
    trim: true
  },
  expiryDate: {
    type: Date,
    index: true
  },
  unitOfMeasure: {
    type: String,
    required: true,
    trim: true
  },
  currentStock: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  minimumStock: {
    type: Number,
    required: true,
    min: 0,
    default: 10
  },
  maximumStock: {
    type: Number,
    required: true,
    min: 0
  },
  reorderLevel: {
    type: Number,
    required: true,
    min: 0
  },
  unitCost: {
    type: Number,
    required: true,
    min: 0
  },
  sellingPrice: {
    type: Number,
    min: 0
  },
  location: {
    building: String,
    floor: String,
    room: String,
    shelf: String
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Expired', 'Recalled'],
    default: 'Active',
    index: true
  },
  isControlledSubstance: {
    type: Boolean,
    default: false
  },
  requiresPrescription: {
    type: Boolean,
    default: false
  },
  storageConditions: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true
  },
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Prescription schema
const PrescriptionSchema = new mongoose.Schema({
  prescriptionId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'RX' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  medications: [{
    medication: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'InventoryItem',
      required: true
    },
    dosage: { type: String, required: true },
    frequency: { type: String, required: true },
    duration: { type: String, required: true },
    quantity: { type: Number, required: true, min: 1 },
    instructions: String,
    substitutionAllowed: { type: Boolean, default: true }
  }],
  status: {
    type: String,
    enum: ['Pending', 'Dispensed', 'Partially Dispensed', 'Cancelled', 'Expired'],
    default: 'Pending'
  },
  prescriptionDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  expiryDate: {
    type: Date,
    required: true,
    default: function() {
      const date = new Date();
      date.setDate(date.getDate() + 30); // 30 days validity
      return date;
    }
  },
  dispensedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  dispensedDate: Date,
  totalCost: {
    type: Number,
    min: 0
  },
  insurance: {
    covered: { type: Boolean, default: false },
    claimNumber: String,
    copayAmount: Number
  },
  notes: String
}, {
  timestamps: true
});

// Pharmacy Bill Schema
const PharmacyBillSchema = new mongoose.Schema({
  billNumber: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'PB' + Date.now().toString().slice(-8);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  prescription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Prescription'
  },
  items: [{
    inventoryItem: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'InventoryItem',
      required: true
    },
    name: { type: String, required: true },
    quantity: { type: Number, required: true, min: 1 },
    unitPrice: { type: Number, required: true, min: 0 },
    totalPrice: { type: Number, required: true, min: 0 },
    batchNumber: String,
    expiryDate: Date
  }],
  subtotal: { type: Number, required: true, min: 0 },
  tax: { type: Number, default: 0, min: 0 },
  discount: { type: Number, default: 0, min: 0 },
  total: { type: Number, required: true, min: 0 },
  paymentMethod: {
    type: String,
    enum: ['Cash', 'Card', 'Insurance', 'Bank Transfer', 'Check'],
    default: 'Cash'
  },
  paymentStatus: {
    type: String,
    enum: ['Pending', 'Paid', 'Partially Paid', 'Refunded'],
    default: 'Pending'
  },
  billedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  billDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  notes: String
}, {
  timestamps: true
});

// Medical Prescription Schema (for medical module)
const MedicalPrescriptionSchema = new mongoose.Schema({
  prescriptionId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'MP' + Date.now().toString().slice(-8);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  diagnosis: String,
  medications: [{
    name: { type: String, required: true },
    dosage: { type: String, required: true },
    frequency: { type: String, required: true },
    duration: { type: String, required: true },
    instructions: String,
    quantity: { type: Number, required: true, min: 1 }
  }],
  instructions: String,
  followUpDate: Date,
  status: {
    type: String,
    enum: ['Active', 'Completed', 'Cancelled'],
    default: 'Active'
  },
  prescriptionDate: {
    type: Date,
    required: true,
    default: Date.now
  }
}, {
  timestamps: true
});

// Virtual for stock status
InventoryItemSchema.virtual('stockStatus').get(function() {
  if (this.currentStock <= 0) return 'Out of Stock';
  if (this.currentStock <= this.reorderLevel) return 'Low Stock';
  if (this.currentStock <= this.minimumStock) return 'Below Minimum';
  return 'In Stock';
});

// Virtual for days until expiry
InventoryItemSchema.virtual('daysUntilExpiry').get(function() {
  if (!this.expiryDate) return null;
  const today = new Date();
  const expiry = new Date(this.expiryDate);
  const diffTime = expiry.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Indexes
InventoryItemSchema.index({ itemId: 1 });
InventoryItemSchema.index({ name: 'text', description: 'text' });
InventoryItemSchema.index({ category: 1, subcategory: 1 });
InventoryItemSchema.index({ currentStock: 1 });
InventoryItemSchema.index({ expiryDate: 1 });

PrescriptionSchema.index({ prescriptionId: 1 });
PrescriptionSchema.index({ patient: 1 });
PrescriptionSchema.index({ doctor: 1 });
PrescriptionSchema.index({ status: 1 });
PrescriptionSchema.index({ prescriptionDate: 1 });

// Get existing models or create new ones
let InventoryItem, Prescription, PharmacyBill, MedicalPrescription;
try {
  InventoryItem = mongoose.model('InventoryItem');
  Prescription = mongoose.model('Prescription');
  PharmacyBill = mongoose.model('PharmacyBill');
  MedicalPrescription = mongoose.model('MedicalPrescription');
} catch (error) {
  InventoryItem = mongoose.model('InventoryItem', InventoryItemSchema);
  Prescription = mongoose.model('Prescription', PrescriptionSchema);
  PharmacyBill = mongoose.model('PharmacyBill', PharmacyBillSchema);
  MedicalPrescription = mongoose.model('MedicalPrescription', MedicalPrescriptionSchema);
}

// @desc    Get all inventory items with pagination and filtering
// @route   GET /api/pharmacy/inventory
// @access  Private
export const getInventoryItems = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.category) {
      filter.category = req.query.category;
    }
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.lowStock === 'true') {
      filter.$expr = { $lte: ['$currentStock', '$reorderLevel'] };
    }
    
    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }

    const items = await InventoryItem.find(filter)
      .populate('lastUpdatedBy', 'firstName lastName')
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit);

    const total = await InventoryItem.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get inventory items error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching inventory items'
    });
  }
};

// @desc    Get single inventory item
// @route   GET /api/pharmacy/inventory/:id
// @access  Private
export const getInventoryItem = async (req, res) => {
  try {
    const item = await InventoryItem.findById(req.params.id)
      .populate('lastUpdatedBy', 'firstName lastName email');

    if (!item) {
      return res.status(404).json({
        success: false,
        error: 'Inventory item not found'
      });
    }

    res.status(200).json({
      success: true,
      data: item
    });
  } catch (error) {
    console.error('Get inventory item error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching inventory item'
    });
  }
};

// @desc    Create new inventory item
// @route   POST /api/pharmacy/inventory
// @access  Private
export const createInventoryItem = async (req, res) => {
  try {
    req.body.lastUpdatedBy = req.user._id;

    const item = await InventoryItem.create(req.body);

    const populatedItem = await InventoryItem.findById(item._id)
      .populate('lastUpdatedBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedItem
    });
  } catch (error) {
    console.error('Create inventory item error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating inventory item'
    });
  }
};

// @desc    Update inventory item
// @route   PUT /api/pharmacy/inventory/:id
// @access  Private
export const updateInventoryItem = async (req, res) => {
  try {
    const item = await InventoryItem.findById(req.params.id);

    if (!item) {
      return res.status(404).json({
        success: false,
        error: 'Inventory item not found'
      });
    }

    req.body.lastUpdatedBy = req.user._id;

    const updatedItem = await InventoryItem.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('lastUpdatedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedItem
    });
  } catch (error) {
    console.error('Update inventory item error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating inventory item'
    });
  }
};

// @desc    Get all prescriptions with pagination and filtering
// @route   GET /api/pharmacy/prescriptions
// @access  Private
export const getPrescriptions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.patient) {
      filter.patient = req.query.patient;
    }
    
    if (req.query.doctor) {
      filter.doctor = req.query.doctor;
    }

    const prescriptions = await Prescription.find(filter)
      .populate('patient', 'firstName lastName patientId phone')
      .populate('doctor', 'firstName lastName department')
      .populate('medications.medication', 'name unitOfMeasure sellingPrice')
      .populate('dispensedBy', 'firstName lastName')
      .sort({ prescriptionDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Prescription.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: prescriptions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get prescriptions error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching prescriptions'
    });
  }
};

// @desc    Create new prescription
// @route   POST /api/pharmacy/prescriptions
// @access  Private
export const createPrescription = async (req, res) => {
  try {
    const prescription = await Prescription.create(req.body);

    const populatedPrescription = await Prescription.findById(prescription._id)
      .populate('patient', 'firstName lastName patientId phone')
      .populate('doctor', 'firstName lastName department')
      .populate('medications.medication', 'name unitOfMeasure sellingPrice');

    res.status(201).json({
      success: true,
      data: populatedPrescription
    });
  } catch (error) {
    console.error('Create prescription error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating prescription'
    });
  }
};

// @desc    Get pharmacy statistics
// @route   GET /api/pharmacy/stats
// @access  Private
export const getPharmacyStats = async (req, res) => {
  try {
    const stats = await Promise.all([
      // Total inventory items
      InventoryItem.countDocuments({ status: 'Active' }),
      // Low stock items
      InventoryItem.countDocuments({
        status: 'Active',
        $expr: { $lte: ['$currentStock', '$reorderLevel'] }
      }),
      // Expired items
      InventoryItem.countDocuments({
        status: 'Active',
        expiryDate: { $lt: new Date() }
      }),
      // Pending prescriptions
      Prescription.countDocuments({ status: 'Pending' })
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalItems: stats[0],
        lowStockItems: stats[1],
        expiredItems: stats[2],
        pendingPrescriptions: stats[3]
      }
    });
  } catch (error) {
    console.error('Get pharmacy stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching pharmacy statistics'
    });
  }
};

// @desc    Create pharmacy bill
// @route   POST /api/pharmacy/bills
// @access  Private
export const createPharmacyBill = async (req, res) => {
  try {
    const { patientId, items, paymentMethod, discount = 0, notes } = req.body;

    // Validate items and calculate totals
    let subtotal = 0;
    const billItems = [];

    for (const item of items) {
      const inventoryItem = await InventoryItem.findById(item.inventoryItemId);
      if (!inventoryItem) {
        return res.status(404).json({
          success: false,
          error: `Inventory item not found: ${item.inventoryItemId}`
        });
      }

      if (inventoryItem.currentStock < item.quantity) {
        return res.status(400).json({
          success: false,
          error: `Insufficient stock for ${inventoryItem.name}. Available: ${inventoryItem.currentStock}, Requested: ${item.quantity}`
        });
      }

      const totalPrice = item.quantity * inventoryItem.sellingPrice;
      subtotal += totalPrice;

      billItems.push({
        inventoryItem: inventoryItem._id,
        name: inventoryItem.name,
        quantity: item.quantity,
        unitPrice: inventoryItem.sellingPrice,
        totalPrice,
        batchNumber: inventoryItem.batchNumber,
        expiryDate: inventoryItem.expiryDate
      });

      // Update inventory stock
      inventoryItem.currentStock -= item.quantity;
      await inventoryItem.save();
    }

    // Calculate tax (8% default)
    const tax = subtotal * 0.08;
    const total = subtotal + tax - discount;

    // Create bill
    const bill = new PharmacyBill({
      patient: patientId,
      items: billItems,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      paymentStatus: 'Paid',
      billedBy: req.user.id,
      notes
    });

    await bill.save();

    // Populate patient details for response
    await bill.populate('patient', 'firstName lastName patientId');

    res.status(201).json({
      success: true,
      data: bill,
      message: 'Pharmacy bill created successfully'
    });
  } catch (error) {
    console.error('Create pharmacy bill error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating pharmacy bill'
    });
  }
};

// @desc    Get pharmacy bills with pagination
// @route   GET /api/pharmacy/bills
// @access  Private
export const getPharmacyBills = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = {};

    if (req.query.patientId) {
      filter.patient = req.query.patientId;
    }

    if (req.query.paymentStatus) {
      filter.paymentStatus = req.query.paymentStatus;
    }

    if (req.query.startDate && req.query.endDate) {
      filter.billDate = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    }

    const bills = await PharmacyBill.find(filter)
      .populate('patient', 'firstName lastName patientId')
      .populate('billedBy', 'firstName lastName')
      .sort({ billDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await PharmacyBill.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: bills,
      pagination: {
        page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Get pharmacy bills error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching pharmacy bills'
    });
  }
};

// @desc    Generate pharmacy bill PDF
// @route   GET /api/pharmacy/bills/:id/pdf
// @access  Private
export const generateBillPDF = async (req, res) => {
  try {
    const bill = await PharmacyBill.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('billedBy', 'firstName lastName');

    if (!bill) {
      return res.status(404).json({
        success: false,
        error: 'Bill not found'
      });
    }

    const billData = {
      billNumber: bill.billNumber,
      patientName: `${bill.patient.firstName} ${bill.patient.lastName}`,
      patientId: bill.patient.patientId,
      date: bill.billDate,
      items: bill.items,
      subtotal: bill.subtotal,
      tax: bill.tax,
      discount: bill.discount,
      total: bill.total,
      paymentMethod: bill.paymentMethod,
      hospitalInfo: {
        name: 'Hospital Management System',
        address: '123 Medical Center Drive, Healthcare City, HC 12345',
        phone: '+****************',
        email: '<EMAIL>'
      }
    };

    const pdfBuffer = await pdfService.generateBill(billData);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="bill-${bill.billNumber}.pdf"`);
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Generate bill PDF error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while generating bill PDF'
    });
  }
};

// @desc    Create medical prescription
// @route   POST /api/medical/prescriptions
// @access  Private
export const createMedicalPrescription = async (req, res) => {
  try {
    const { patientId, diagnosis, medications, instructions, followUpDate } = req.body;

    const prescription = new MedicalPrescription({
      patient: patientId,
      doctor: req.user.id,
      diagnosis,
      medications,
      instructions,
      followUpDate
    });

    await prescription.save();
    await prescription.populate('patient', 'firstName lastName patientId age gender');
    await prescription.populate('doctor', 'firstName lastName specialty');

    res.status(201).json({
      success: true,
      data: prescription,
      message: 'Medical prescription created successfully'
    });
  } catch (error) {
    console.error('Create medical prescription error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating medical prescription'
    });
  }
};

// @desc    Get medical prescriptions
// @route   GET /api/medical/prescriptions
// @access  Private
export const getMedicalPrescriptions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = {};

    if (req.query.patientId) {
      filter.patient = req.query.patientId;
    }

    if (req.query.doctorId) {
      filter.doctor = req.query.doctorId;
    }

    if (req.query.status) {
      filter.status = req.query.status;
    }

    const prescriptions = await MedicalPrescription.find(filter)
      .populate('patient', 'firstName lastName patientId age gender')
      .populate('doctor', 'firstName lastName specialty')
      .sort({ prescriptionDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await MedicalPrescription.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: prescriptions,
      pagination: {
        page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Get medical prescriptions error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching medical prescriptions'
    });
  }
};

// @desc    Generate prescription PDF
// @route   GET /api/medical/prescriptions/:id/pdf
// @access  Private
export const generatePrescriptionPDF = async (req, res) => {
  try {
    const prescription = await MedicalPrescription.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId age gender')
      .populate('doctor', 'firstName lastName specialty');

    if (!prescription) {
      return res.status(404).json({
        success: false,
        error: 'Prescription not found'
      });
    }

    const prescriptionData = {
      prescriptionId: prescription.prescriptionId,
      patientName: `${prescription.patient.firstName} ${prescription.patient.lastName}`,
      patientAge: prescription.patient.age,
      patientGender: prescription.patient.gender,
      doctorName: `${prescription.doctor.firstName} ${prescription.doctor.lastName}`,
      doctorSpecialty: prescription.doctor.specialty,
      date: prescription.prescriptionDate,
      medications: prescription.medications,
      diagnosis: prescription.diagnosis,
      instructions: prescription.instructions,
      hospitalInfo: {
        name: 'Hospital Management System',
        address: '123 Medical Center Drive, Healthcare City, HC 12345',
        phone: '+****************',
        email: '<EMAIL>'
      }
    };

    const pdfBuffer = await pdfService.generatePrescription(prescriptionData);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="prescription-${prescription.prescriptionId}.pdf"`);
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Generate prescription PDF error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while generating prescription PDF'
    });
  }
};

// @desc    Generate inventory report PDF
// @route   GET /api/pharmacy/reports/inventory
// @access  Private
export const generateInventoryReportPDF = async (req, res) => {
  try {
    const items = await InventoryItem.find({ status: 'Active' });

    const totalValue = items.reduce((sum, item) => sum + (item.currentStock * item.unitCost), 0);
    const lowStockItems = items.filter(item => item.currentStock <= item.reorderLevel);
    const expiringItems = items.filter(item => {
      if (!item.expiryDate) return false;
      const threeMonthsFromNow = new Date();
      threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);
      return item.expiryDate <= threeMonthsFromNow;
    });

    const inventoryData = {
      reportDate: new Date(),
      items: items.map(item => ({
        name: item.name,
        category: item.category,
        currentStock: item.currentStock,
        reorderLevel: item.reorderLevel,
        unitPrice: item.unitCost,
        expiryDate: item.expiryDate,
        status: item.stockStatus
      })),
      totalValue,
      lowStockItems,
      expiringItems,
      hospitalInfo: {
        name: 'Hospital Management System',
        address: '123 Medical Center Drive, Healthcare City, HC 12345',
        phone: '+****************',
        email: '<EMAIL>'
      }
    };

    const pdfBuffer = await pdfService.generateInventoryReport(inventoryData);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="inventory-report-${new Date().toISOString().split('T')[0]}.pdf"`);
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Generate inventory report PDF error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while generating inventory report PDF'
    });
  }
};

// @desc    Get pharmacy analytics
// @route   GET /api/pharmacy/analytics
// @access  Private
export const getPharmacyAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const dateFilter = {};

    if (startDate && endDate) {
      dateFilter.billDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Get analytics data
    const [
      totalRevenue,
      totalBills,
      totalItems,
      lowStockCount,
      expiringCount,
      recentBills,
      topSellingItems,
      monthlyRevenue
    ] = await Promise.all([
      // Total revenue
      PharmacyBill.aggregate([
        { $match: dateFilter },
        { $group: { _id: null, total: { $sum: '$total' } } }
      ]),
      // Total bills count
      PharmacyBill.countDocuments(dateFilter),
      // Total inventory items
      InventoryItem.countDocuments({ status: 'Active' }),
      // Low stock items count
      InventoryItem.countDocuments({
        status: 'Active',
        $expr: { $lte: ['$currentStock', '$reorderLevel'] }
      }),
      // Expiring items count (next 3 months)
      InventoryItem.countDocuments({
        status: 'Active',
        expiryDate: {
          $lte: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
        }
      }),
      // Recent bills
      PharmacyBill.find(dateFilter)
        .populate('patient', 'firstName lastName')
        .sort({ billDate: -1 })
        .limit(5),
      // Top selling items
      PharmacyBill.aggregate([
        { $match: dateFilter },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.name',
            totalQuantity: { $sum: '$items.quantity' },
            totalRevenue: { $sum: '$items.totalPrice' }
          }
        },
        { $sort: { totalQuantity: -1 } },
        { $limit: 10 }
      ]),
      // Monthly revenue trend
      PharmacyBill.aggregate([
        {
          $group: {
            _id: {
              year: { $year: '$billDate' },
              month: { $month: '$billDate' }
            },
            revenue: { $sum: '$total' },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': -1, '_id.month': -1 } },
        { $limit: 12 }
      ])
    ]);

    res.status(200).json({
      success: true,
      data: {
        summary: {
          totalRevenue: totalRevenue[0]?.total || 0,
          totalBills,
          totalItems,
          lowStockCount,
          expiringCount
        },
        recentBills,
        topSellingItems,
        monthlyRevenue
      }
    });
  } catch (error) {
    console.error('Get pharmacy analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching pharmacy analytics'
    });
  }
};
