import mongoose from 'mongoose';

// Appointment schema
const AppointmentSchema = new mongoose.Schema({
  appointmentId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'APT' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  department: {
    type: String,
    required: true,
    enum: [
      'Cardiology',
      'Neurology',
      'Orthopedics',
      'Pediatrics',
      'Gynecology',
      'Dermatology',
      'Psychiatry',
      'Oncology',
      'Emergency',
      'General Medicine',
      'Surgery',
      'Radiology',
      'Pathology'
    ]
  },
  appointmentDate: {
    type: Date,
    required: true
  },
  appointmentTime: {
    type: String,
    required: true
  },
  duration: {
    type: Number,
    default: 30,
    min: 15,
    max: 240
  },
  type: {
    type: String,
    required: true,
    enum: ['Consultation', 'Follow-up', 'Emergency', 'Surgery', 'Diagnostic']
  },
  status: {
    type: String,
    required: true,
    enum: ['Scheduled', 'Confirmed', 'In Progress', 'Completed', 'Cancelled', 'No Show'],
    default: 'Scheduled'
  },
  priority: {
    type: String,
    required: true,
    enum: ['Low', 'Medium', 'High', 'Emergency'],
    default: 'Medium'
  },
  reason: {
    type: String,
    required: true,
    trim: true
  },
  symptoms: [String],
  notes: {
    type: String,
    trim: true
  },
  diagnosis: {
    type: String,
    trim: true
  },
  prescription: [{
    medication: { type: String, required: true },
    dosage: { type: String, required: true },
    frequency: { type: String, required: true },
    duration: { type: String, required: true },
    instructions: String
  }],
  followUpDate: Date,
  room: String,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better performance
AppointmentSchema.index({ appointmentId: 1 });
AppointmentSchema.index({ patient: 1 });
AppointmentSchema.index({ doctor: 1 });
AppointmentSchema.index({ appointmentDate: 1 });
AppointmentSchema.index({ status: 1 });
AppointmentSchema.index({ department: 1 });

// Compound indexes
AppointmentSchema.index({ doctor: 1, appointmentDate: 1, appointmentTime: 1 });
AppointmentSchema.index({ patient: 1, appointmentDate: 1 });

// Get existing model or create new one
let Appointment;
try {
  Appointment = mongoose.model('Appointment');
} catch (error) {
  Appointment = mongoose.model('Appointment', AppointmentSchema);
}

// @desc    Get all appointments with pagination and filtering
// @route   GET /api/appointments
// @access  Private
export const getAppointments = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.department) {
      filter.department = req.query.department;
    }
    
    if (req.query.doctor) {
      filter.doctor = req.query.doctor;
    }
    
    if (req.query.patient) {
      filter.patient = req.query.patient;
    }
    
    if (req.query.date) {
      const startDate = new Date(req.query.date);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);
      filter.appointmentDate = {
        $gte: startDate,
        $lt: endDate
      };
    }

    const appointments = await Appointment.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .populate('createdBy', 'firstName lastName')
      .sort({ appointmentDate: 1, appointmentTime: 1 })
      .skip(skip)
      .limit(limit);

    const total = await Appointment.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: appointments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get appointments error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching appointments'
    });
  }
};

// @desc    Get single appointment
// @route   GET /api/appointments/:id
// @access  Private
export const getAppointment = async (req, res) => {
  try {
    const appointment = await Appointment.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId phone email dateOfBirth gender')
      .populate('doctor', 'firstName lastName department email')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName');

    if (!appointment) {
      return res.status(404).json({
        success: false,
        error: 'Appointment not found'
      });
    }

    res.status(200).json({
      success: true,
      data: appointment
    });
  } catch (error) {
    console.error('Get appointment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching appointment'
    });
  }
};

// @desc    Create new appointment
// @route   POST /api/appointments
// @access  Private
export const createAppointment = async (req, res) => {
  try {
    // Check for scheduling conflicts
    const conflictingAppointment = await Appointment.findOne({
      doctor: req.body.doctor,
      appointmentDate: req.body.appointmentDate,
      appointmentTime: req.body.appointmentTime,
      status: { $nin: ['Cancelled', 'Completed'] }
    });

    if (conflictingAppointment) {
      return res.status(400).json({
        success: false,
        error: 'Doctor is not available at this time slot'
      });
    }

    // Set createdBy to current user
    req.body.createdBy = req.user._id;

    const appointment = await Appointment.create(req.body);

    // Populate the created appointment
    const populatedAppointment = await Appointment.findById(appointment._id)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .populate('createdBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedAppointment
    });
  } catch (error) {
    console.error('Create appointment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating appointment'
    });
  }
};

// @desc    Update appointment
// @route   PUT /api/appointments/:id
// @access  Private
export const updateAppointment = async (req, res) => {
  try {
    const appointment = await Appointment.findById(req.params.id);

    if (!appointment) {
      return res.status(404).json({
        success: false,
        error: 'Appointment not found'
      });
    }

    // Check for scheduling conflicts if time/date/doctor is being changed
    if (req.body.doctor || req.body.appointmentDate || req.body.appointmentTime) {
      const doctor = req.body.doctor || appointment.doctor;
      const appointmentDate = req.body.appointmentDate || appointment.appointmentDate;
      const appointmentTime = req.body.appointmentTime || appointment.appointmentTime;

      const conflictingAppointment = await Appointment.findOne({
        _id: { $ne: req.params.id },
        doctor: doctor,
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        status: { $nin: ['Cancelled', 'Completed'] }
      });

      if (conflictingAppointment) {
        return res.status(400).json({
          success: false,
          error: 'Doctor is not available at this time slot'
        });
      }
    }

    // Set updatedBy to current user
    req.body.updatedBy = req.user._id;

    const updatedAppointment = await Appointment.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedAppointment
    });
  } catch (error) {
    console.error('Update appointment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating appointment'
    });
  }
};

// @desc    Delete appointment
// @route   DELETE /api/appointments/:id
// @access  Private
export const deleteAppointment = async (req, res) => {
  try {
    const appointment = await Appointment.findById(req.params.id);

    if (!appointment) {
      return res.status(404).json({
        success: false,
        error: 'Appointment not found'
      });
    }

    // Instead of deleting, mark as cancelled
    appointment.status = 'Cancelled';
    appointment.updatedBy = req.user._id;
    await appointment.save();

    res.status(200).json({
      success: true,
      message: 'Appointment cancelled successfully'
    });
  } catch (error) {
    console.error('Delete appointment error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while cancelling appointment'
    });
  }
};

// @desc    Get doctor's schedule
// @route   GET /api/appointments/doctor/:doctorId/schedule
// @access  Private
export const getDoctorSchedule = async (req, res) => {
  try {
    const { doctorId } = req.params;
    const { date } = req.query;

    let filter = {
      doctor: doctorId,
      status: { $nin: ['Cancelled'] }
    };

    if (date) {
      const startDate = new Date(date);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);
      filter.appointmentDate = {
        $gte: startDate,
        $lt: endDate
      };
    }

    const appointments = await Appointment.find(filter)
      .populate('patient', 'firstName lastName patientId')
      .sort({ appointmentDate: 1, appointmentTime: 1 });

    res.status(200).json({
      success: true,
      data: appointments
    });
  } catch (error) {
    console.error('Get doctor schedule error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching doctor schedule'
    });
  }
};

// @desc    Get available time slots
// @route   GET /api/appointments/available-slots
// @access  Private
export const getAvailableSlots = async (req, res) => {
  try {
    const { doctorId, date } = req.query;

    if (!doctorId || !date) {
      return res.status(400).json({
        success: false,
        error: 'Doctor ID and date are required'
      });
    }

    const appointmentDate = new Date(date);
    const endDate = new Date(appointmentDate);
    endDate.setDate(endDate.getDate() + 1);

    // Get existing appointments for the doctor on that date
    const existingAppointments = await Appointment.find({
      doctor: doctorId,
      appointmentDate: {
        $gte: appointmentDate,
        $lt: endDate
      },
      status: { $nin: ['Cancelled'] }
    }).select('appointmentTime duration');

    // Generate available time slots (9 AM to 5 PM, 30-minute slots)
    const allSlots = [];
    for (let hour = 9; hour < 17; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        allSlots.push(timeString);
      }
    }

    // Filter out booked slots
    const bookedTimes = existingAppointments.map(apt => apt.appointmentTime);
    const availableSlots = allSlots.filter(slot => !bookedTimes.includes(slot));

    res.status(200).json({
      success: true,
      data: {
        date: date,
        doctorId: doctorId,
        availableSlots: availableSlots,
        bookedSlots: bookedTimes
      }
    });
  } catch (error) {
    console.error('Get available slots error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching available slots'
    });
  }
};
