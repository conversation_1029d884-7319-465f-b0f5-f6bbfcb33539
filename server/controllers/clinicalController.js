import mongoose from 'mongoose';

// Medical Record schema
const MedicalRecordSchema = new mongoose.Schema({
  recordId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'MR' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  visitDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  chiefComplaint: {
    type: String,
    required: true,
    trim: true
  },
  historyOfPresentIllness: {
    type: String,
    required: true,
    trim: true
  },
  physicalExamination: {
    vitalSigns: {
      temperature: { type: Number, min: 90, max: 110 },
      bloodPressure: {
        systolic: { type: Number, min: 70, max: 250 },
        diastolic: { type: Number, min: 40, max: 150 }
      },
      heartRate: { type: Number, min: 30, max: 200 },
      respiratoryRate: { type: Number, min: 8, max: 40 },
      oxygenSaturation: { type: Number, min: 70, max: 100 },
      weight: { type: Number, min: 0.5, max: 500 },
      height: { type: Number, min: 30, max: 250 },
      bmi: Number
    },
    generalAppearance: String,
    systemicExamination: String
  },
  diagnosis: {
    primary: { type: String, required: true },
    secondary: [String],
    icdCodes: [String]
  },
  treatment: {
    medications: [{
      name: { type: String, required: true },
      dosage: { type: String, required: true },
      frequency: { type: String, required: true },
      duration: { type: String, required: true },
      route: { type: String, required: true },
      instructions: String
    }],
    procedures: [{
      name: { type: String, required: true },
      date: { type: Date, required: true },
      notes: String
    }],
    recommendations: [String]
  },
  labResults: [{
    testName: { type: String, required: true },
    result: { type: String, required: true },
    normalRange: String,
    date: { type: Date, required: true },
    status: {
      type: String,
      enum: ['Normal', 'Abnormal', 'Critical'],
      required: true
    }
  }],
  imagingResults: [{
    type: { type: String, required: true },
    date: { type: Date, required: true },
    findings: { type: String, required: true },
    radiologist: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  followUp: {
    required: { type: Boolean, default: false },
    date: Date,
    instructions: String
  },
  notes: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['Draft', 'Completed', 'Reviewed', 'Archived'],
    default: 'Draft'
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: Date
}, {
  timestamps: true
});

// Calculate BMI before saving
MedicalRecordSchema.pre('save', function(next) {
  if (this.physicalExamination?.vitalSigns?.weight && this.physicalExamination?.vitalSigns?.height) {
    const heightInMeters = this.physicalExamination.vitalSigns.height / 100;
    this.physicalExamination.vitalSigns.bmi = Number(
      (this.physicalExamination.vitalSigns.weight / (heightInMeters * heightInMeters)).toFixed(2)
    );
  }
  next();
});

// Indexes
MedicalRecordSchema.index({ recordId: 1 });
MedicalRecordSchema.index({ patient: 1 });
MedicalRecordSchema.index({ doctor: 1 });
MedicalRecordSchema.index({ visitDate: 1 });
MedicalRecordSchema.index({ status: 1 });

// Get existing model or create new one
let MedicalRecord;
try {
  MedicalRecord = mongoose.model('MedicalRecord');
} catch (error) {
  MedicalRecord = mongoose.model('MedicalRecord', MedicalRecordSchema);
}

// @desc    Get all medical records with pagination and filtering
// @route   GET /api/clinical/medical-records
// @access  Private
export const getMedicalRecords = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.patient) {
      filter.patient = req.query.patient;
    }
    
    if (req.query.doctor) {
      filter.doctor = req.query.doctor;
    }
    
    if (req.query.dateFrom || req.query.dateTo) {
      filter.visitDate = {};
      if (req.query.dateFrom) {
        filter.visitDate.$gte = new Date(req.query.dateFrom);
      }
      if (req.query.dateTo) {
        filter.visitDate.$lte = new Date(req.query.dateTo);
      }
    }

    const records = await MedicalRecord.find(filter)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime')
      .populate('reviewedBy', 'firstName lastName')
      .sort({ visitDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await MedicalRecord.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: records,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get medical records error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching medical records'
    });
  }
};

// @desc    Get single medical record
// @route   GET /api/clinical/medical-records/:id
// @access  Private
export const getMedicalRecord = async (req, res) => {
  try {
    const record = await MedicalRecord.findById(req.params.id)
      .populate('patient', 'firstName lastName patientId phone email dateOfBirth gender')
      .populate('doctor', 'firstName lastName department email')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime')
      .populate('reviewedBy', 'firstName lastName');

    if (!record) {
      return res.status(404).json({
        success: false,
        error: 'Medical record not found'
      });
    }

    res.status(200).json({
      success: true,
      data: record
    });
  } catch (error) {
    console.error('Get medical record error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching medical record'
    });
  }
};

// @desc    Create new medical record
// @route   POST /api/clinical/medical-records
// @access  Private
export const createMedicalRecord = async (req, res) => {
  try {
    const record = await MedicalRecord.create(req.body);

    // Populate the created record
    const populatedRecord = await MedicalRecord.findById(record._id)
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime');

    res.status(201).json({
      success: true,
      data: populatedRecord
    });
  } catch (error) {
    console.error('Create medical record error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating medical record'
    });
  }
};

// @desc    Update medical record
// @route   PUT /api/clinical/medical-records/:id
// @access  Private
export const updateMedicalRecord = async (req, res) => {
  try {
    const record = await MedicalRecord.findById(req.params.id);

    if (!record) {
      return res.status(404).json({
        success: false,
        error: 'Medical record not found'
      });
    }

    const updatedRecord = await MedicalRecord.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    )
      .populate('patient', 'firstName lastName patientId phone email')
      .populate('doctor', 'firstName lastName department')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime')
      .populate('reviewedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedRecord
    });
  } catch (error) {
    console.error('Update medical record error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating medical record'
    });
  }
};

// @desc    Delete medical record
// @route   DELETE /api/clinical/medical-records/:id
// @access  Private
export const deleteMedicalRecord = async (req, res) => {
  try {
    const record = await MedicalRecord.findById(req.params.id);

    if (!record) {
      return res.status(404).json({
        success: false,
        error: 'Medical record not found'
      });
    }

    // Archive instead of delete
    record.status = 'Archived';
    await record.save();

    res.status(200).json({
      success: true,
      message: 'Medical record archived successfully'
    });
  } catch (error) {
    console.error('Delete medical record error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while archiving medical record'
    });
  }
};

// @desc    Get patient's medical history
// @route   GET /api/clinical/patients/:patientId/history
// @access  Private
export const getPatientMedicalHistory = async (req, res) => {
  try {
    const { patientId } = req.params;

    const records = await MedicalRecord.find({ patient: patientId })
      .populate('doctor', 'firstName lastName department')
      .populate('appointment', 'appointmentId appointmentDate appointmentTime')
      .sort({ visitDate: -1 });

    res.status(200).json({
      success: true,
      data: records
    });
  } catch (error) {
    console.error('Get patient medical history error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patient medical history'
    });
  }
};
