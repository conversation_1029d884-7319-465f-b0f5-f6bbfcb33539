import mongoose from 'mongoose';

// Advanced Bill Item Schema with comprehensive fields
const billItemSchema = new mongoose.Schema({
  itemId: {
    type: String,
    required: true,
    default: () => 'ITM' + Date.now().toString().slice(-8)
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  serviceCode: {
    type: String,
    trim: true,
    index: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  unitPrice: {
    type: Number,
    required: true,
    min: 0
  },
  totalPrice: {
    type: Number,
    required: true,
    min: 0
  },
  category: {
    type: String,
    required: true,
    enum: [
      // Patient Services
      'Consultation', 'Lab Test', 'Imaging', 'Medication', 'Surgery', 'Room Charges',
      'Emergency', 'Therapy', 'Equipment', 'Nursing Care', 'ICU Charges', 'OT Charges',
      'Physiotherapy', 'Dialysis', 'Blood Bank', 'Ambulance', 'Vaccination',

      // Administrative Services
      'Registration Fee', 'Medical Records', 'Certificate Fee', 'Report Fee',

      // Vendor/Supplier Bills
      'Medical Supplies', 'Pharmaceutical Supplies', 'Equipment Purchase', 'Equipment Maintenance',
      'Utilities', 'Facility Maintenance', 'Cleaning Services', 'Security Services',
      'IT Services', 'Legal Services', 'Consulting Services', 'Insurance Premium',

      // Employee Related
      'Salary', 'Overtime', 'Bonus', 'Travel Allowance', 'Medical Allowance',
      'Training Expenses', 'Recruitment Expenses',

      // Other
      'Other'
    ]
  },
  department: {
    type: String,
    trim: true
  },
  provider: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  discountType: {
    type: String,
    enum: ['Fixed', 'Percentage'],
    default: 'Fixed'
  },
  taxRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  taxAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  netAmount: {
    type: Number,
    required: true,
    min: 0
  },
  isInsuranceCovered: {
    type: Boolean,
    default: false
  },
  insuranceCoverage: {
    type: Number,
    default: 0,
    min: 0
  }
});

// Advanced Payment Schema
const paymentSchema = new mongoose.Schema({
  paymentId: {
    type: String,
    required: true,
    unique: true,
    default: () => 'PAY' + Date.now().toString().slice(-8)
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: ['Cash', 'Credit Card', 'Debit Card', 'Bank Transfer', 'Check', 'Insurance', 'Online Payment', 'UPI', 'Wallet', 'EMI']
  },
  paymentDate: {
    type: Date,
    default: Date.now
  },
  transactionId: {
    type: String,
    trim: true
  },
  referenceNumber: {
    type: String,
    trim: true
  },
  paymentStatus: {
    type: String,
    enum: ['Pending', 'Completed', 'Failed', 'Refunded', 'Cancelled'],
    default: 'Completed'
  },
  bankDetails: {
    bankName: String,
    accountNumber: String,
    ifscCode: String,
    checkNumber: String
  },
  cardDetails: {
    last4Digits: String,
    cardType: String,
    authCode: String
  },
  notes: {
    type: String,
    trim: true
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  refundDetails: {
    refundAmount: Number,
    refundDate: Date,
    refundReason: String,
    refundedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: true
});

// Advanced Bill Schema
const BillSchema = new mongoose.Schema({
  billId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'BILL' + Date.now().toString().slice(-6);
    }
  },
  billNumber: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const timestamp = Date.now().toString().slice(-6);
      return `HMS-${year}${month}-${timestamp}`;
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    index: true
  },

  // Vendor/Supplier Information (for non-patient bills)
  vendor: {
    vendorId: {
      type: String,
      trim: true
    },
    name: {
      type: String,
      trim: true
    },
    contactPerson: {
      type: String,
      trim: true
    },
    phone: {
      type: String,
      trim: true
    },
    email: {
      type: String,
      trim: true
    },
    address: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String
    },
    taxId: {
      type: String,
      trim: true
    },
    bankDetails: {
      bankName: String,
      accountNumber: String,
      routingNumber: String,
      swiftCode: String
    }
  },

  // Employee Information (for employee-related bills)
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  billDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true,
    default: function() {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date;
    }
  },
  items: [billItemSchema],

  // Financial Calculations
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  taxAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  taxRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  discountAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  discountType: {
    type: String,
    enum: ['Fixed', 'Percentage'],
    default: 'Fixed'
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  paidAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  balanceAmount: {
    type: Number,
    required: true,
    min: 0
  },

  // Advanced Financial Fields
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'INR', 'CAD', 'AUD', 'JPY', 'CNY']
  },
  exchangeRate: {
    type: Number,
    default: 1,
    min: 0
  },
  costCenter: {
    type: String,
    trim: true
  },
  budgetCode: {
    type: String,
    trim: true
  },
  projectCode: {
    type: String,
    trim: true
  },
  glAccount: {
    type: String,
    trim: true
  },
  poNumber: {
    type: String,
    trim: true
  },
  invoiceNumber: {
    type: String,
    trim: true
  },
  deliveryDate: Date,
  serviceDate: Date,

  // Payment Information
  paymentStatus: {
    type: String,
    enum: ['Pending', 'Partial', 'Paid', 'Overdue', 'Cancelled', 'Refunded'],
    default: 'Pending',
    index: true
  },
  paymentTerms: {
    type: String,
    enum: ['Immediate', 'Net 15', 'Net 30', 'Net 60', 'Net 90', 'Custom'],
    default: 'Net 30'
  },
  payments: [paymentSchema],

  // Bill Classification
  billType: {
    type: String,
    enum: [
      // Patient Bills
      'Patient Bill', 'Emergency Bill', 'Insurance Bill', 'Corporate Bill',
      'Government Bill', 'Charity Bill', 'Cash Bill', 'Credit Bill',

      // Vendor/Supplier Bills
      'Vendor Bill', 'Supplier Bill', 'Utility Bill', 'Service Bill',
      'Equipment Bill', 'Maintenance Bill', 'Rental Bill',

      // Internal Bills
      'Employee Expense', 'Payroll', 'Petty Cash', 'Advance Payment',
      'Reimbursement', 'Inter-Department Transfer',

      // Other
      'Miscellaneous'
    ],
    default: 'Patient Bill'
  },
  priority: {
    type: String,
    enum: ['Low', 'Normal', 'High', 'Urgent'],
    default: 'Normal'
  },
  category: {
    type: String,
    enum: [
      // Patient Categories
      'Inpatient', 'Outpatient', 'Emergency', 'Diagnostic', 'Pharmacy', 'Surgery',
      'ICU', 'OPD', 'IPD', 'Day Care', 'Home Care',

      // Service Categories
      'Laboratory', 'Radiology', 'Pathology', 'Cardiology', 'Neurology',
      'Orthopedics', 'Pediatrics', 'Gynecology', 'Oncology', 'Psychiatry',

      // Administrative Categories
      'Administrative', 'Financial', 'Operational', 'Maintenance'
    ],
    default: 'Outpatient'
  },

  // Insurance Information
  insurance: {
    provider: {
      type: String,
      trim: true
    },
    policyNumber: {
      type: String,
      trim: true
    },
    claimNumber: {
      type: String,
      trim: true
    },
    preAuthNumber: {
      type: String,
      trim: true
    },
    coverageAmount: {
      type: Number,
      min: 0
    },
    approvedAmount: {
      type: Number,
      min: 0
    },
    deductible: {
      type: Number,
      min: 0,
      default: 0
    },
    copayAmount: {
      type: Number,
      min: 0,
      default: 0
    },
    status: {
      type: String,
      enum: ['Pending', 'Approved', 'Rejected', 'Partial', 'Under Review'],
      default: 'Pending'
    },
    submissionDate: Date,
    approvalDate: Date,
    rejectionReason: String,
    contactPerson: String,
    contactPhone: String
  },

  // Additional Information
  notes: {
    type: String,
    trim: true
  },
  internalNotes: {
    type: String,
    trim: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  department: {
    type: String,
    trim: true
  },
  location: {
    type: String,
    trim: true,
    default: 'Main Hospital'
  },

  // Audit Trail
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvalDate: Date,

  // Status and Control
  isActive: {
    type: Boolean,
    default: true
  },
  isVoided: {
    type: Boolean,
    default: false
  },
  voidReason: String,
  voidedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  voidedAt: Date,

  // Financial Year and Reporting
  fiscalYear: {
    type: String,
    default: () => {
      const now = new Date();
      const year = now.getFullYear();
      return `${year}-${year + 1}`;
    }
  },
  quarter: {
    type: String,
    default: () => {
      const month = new Date().getMonth() + 1;
      if (month <= 3) return 'Q1';
      if (month <= 6) return 'Q2';
      if (month <= 9) return 'Q3';
      return 'Q4';
    }
  }
}, {
  timestamps: true
});

// Comprehensive Indexes for Performance
BillSchema.index({ billId: 1 });
BillSchema.index({ billNumber: 1 });
BillSchema.index({ patient: 1 });
BillSchema.index({ paymentStatus: 1 });
BillSchema.index({ billDate: 1 });
BillSchema.index({ dueDate: 1 });
BillSchema.index({ billType: 1 });
BillSchema.index({ department: 1 });
BillSchema.index({ fiscalYear: 1 });
BillSchema.index({ quarter: 1 });
BillSchema.index({ 'insurance.provider': 1 });
BillSchema.index({ 'insurance.status': 1 });
BillSchema.index({ createdAt: -1 });
BillSchema.index({ totalAmount: 1 });
BillSchema.index({ balanceAmount: 1 });

// Compound Indexes
BillSchema.index({ patient: 1, paymentStatus: 1 });
BillSchema.index({ billDate: 1, paymentStatus: 1 });
BillSchema.index({ department: 1, billDate: 1 });
BillSchema.index({ fiscalYear: 1, quarter: 1 });

// Pre-save middleware for calculations and validations
BillSchema.pre('save', function(next) {
  // Calculate balance amount
  this.balanceAmount = this.totalAmount - this.paidAmount;

  // Update payment status based on amounts
  if (this.paidAmount === 0) {
    this.paymentStatus = 'Pending';
  } else if (this.paidAmount >= this.totalAmount) {
    this.paymentStatus = 'Paid';
  } else {
    this.paymentStatus = 'Partial';
  }

  // Check for overdue status
  if (this.paymentStatus !== 'Paid' && new Date() > this.dueDate) {
    this.paymentStatus = 'Overdue';
  }

  // Calculate item-level taxes and totals
  if (this.items && this.items.length > 0) {
    this.items.forEach(item => {
      if (item.taxRate > 0) {
        item.taxAmount = (item.totalPrice * item.taxRate) / 100;
      }
      item.netAmount = item.totalPrice + (item.taxAmount || 0) - (item.discount || 0);
    });
  }

  next();
});

// Instance Methods
BillSchema.methods.addPayment = function(paymentData) {
  this.payments.push(paymentData);
  this.paidAmount = this.payments.reduce((sum, payment) => {
    return payment.paymentStatus === 'Completed' ? sum + payment.amount : sum;
  }, 0);
  return this.save();
};

BillSchema.methods.calculateTotals = function() {
  if (!this.items || this.items.length === 0) return;

  this.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
  this.taxAmount = this.items.reduce((sum, item) => sum + (item.taxAmount || 0), 0);
  this.totalAmount = this.subtotal + this.taxAmount - this.discountAmount;
  this.balanceAmount = this.totalAmount - this.paidAmount;
};

BillSchema.methods.isOverdue = function() {
  return this.paymentStatus !== 'Paid' && new Date() > this.dueDate;
};

BillSchema.methods.getDaysOverdue = function() {
  if (!this.isOverdue()) return 0;
  const today = new Date();
  const diffTime = today - this.dueDate;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

BillSchema.methods.getPaymentHistory = function() {
  return this.payments.sort((a, b) => b.paymentDate - a.paymentDate);
};

// Static Methods
BillSchema.statics.getOverdueBills = function() {
  const today = new Date();
  return this.find({
    paymentStatus: { $in: ['Pending', 'Partial', 'Overdue'] },
    dueDate: { $lt: today },
    isActive: true,
    isVoided: false
  });
};

BillSchema.statics.getRevenueByPeriod = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        billDate: { $gte: startDate, $lte: endDate },
        isActive: true,
        isVoided: false
      }
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$totalAmount' },
        totalPaid: { $sum: '$paidAmount' },
        totalOutstanding: { $sum: '$balanceAmount' },
        billCount: { $sum: 1 }
      }
    }
  ]);
};

BillSchema.statics.getRevenueByDepartment = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        billDate: { $gte: startDate, $lte: endDate },
        isActive: true,
        isVoided: false
      }
    },
    {
      $group: {
        _id: '$department',
        totalRevenue: { $sum: '$totalAmount' },
        totalPaid: { $sum: '$paidAmount' },
        billCount: { $sum: 1 }
      }
    },
    { $sort: { totalRevenue: -1 } }
  ]);
};

BillSchema.statics.getPaymentMethodStats = function(startDate, endDate) {
  return this.aggregate([
    { $unwind: '$payments' },
    {
      $match: {
        'payments.paymentDate': { $gte: startDate, $lte: endDate },
        'payments.paymentStatus': 'Completed'
      }
    },
    {
      $group: {
        _id: '$payments.paymentMethod',
        totalAmount: { $sum: '$payments.amount' },
        count: { $sum: 1 }
      }
    },
    { $sort: { totalAmount: -1 } }
  ]);
};

export default mongoose.model('Bill', BillSchema);
