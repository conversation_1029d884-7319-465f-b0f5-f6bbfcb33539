import mongoose from 'mongoose';

const InventoryItemSchema = new mongoose.Schema({
  itemId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'INV' + Date.now().toString().slice(-6);
    }
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      'Medication',
      'Medical Supplies',
      'Equipment',
      'Consumables',
      'Laboratory Supplies',
      'Surgical Instruments',
      'Other'
    ]
  },
  subcategory: {
    type: String,
    trim: true
  },
  manufacturer: {
    type: String,
    trim: true
  },
  supplier: {
    type: String,
    trim: true
  },
  batchNumber: {
    type: String,
    trim: true
  },
  barcode: {
    type: String,
    trim: true,
    unique: true,
    sparse: true
  },
  unitOfMeasure: {
    type: String,
    required: true,
    enum: ['Piece', 'Box', 'Bottle', 'Vial', 'Tablet', 'Capsule', 'ml', 'mg', 'g', 'kg', 'Other']
  },
  currentStock: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  minimumStock: {
    type: Number,
    required: true,
    min: 0,
    default: 10
  },
  reorderLevel: {
    type: Number,
    required: true,
    min: 0,
    default: 20
  },
  maximumStock: {
    type: Number,
    min: 0
  },
  costPrice: {
    type: Number,
    required: true,
    min: 0
  },
  sellingPrice: {
    type: Number,
    required: true,
    min: 0
  },
  expiryDate: {
    type: Date
  },
  location: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Discontinued', 'Out of Stock'],
    default: 'Active'
  },
  lastRestockDate: Date,
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Indexes
InventoryItemSchema.index({ itemId: 1 });
InventoryItemSchema.index({ name: 1 });
InventoryItemSchema.index({ category: 1 });
InventoryItemSchema.index({ status: 1 });
InventoryItemSchema.index({ expiryDate: 1 });
InventoryItemSchema.index({ currentStock: 1 });

// Virtual for stock status
InventoryItemSchema.virtual('stockStatus').get(function() {
  if (this.currentStock === 0) return 'Out of Stock';
  if (this.currentStock <= this.minimumStock) return 'Low Stock';
  if (this.currentStock <= this.reorderLevel) return 'Reorder Required';
  return 'In Stock';
});

// Virtual for days until expiry
InventoryItemSchema.virtual('daysUntilExpiry').get(function() {
  if (!this.expiryDate) return null;
  const today = new Date();
  const diffTime = this.expiryDate - today;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

export default mongoose.model('InventoryItem', InventoryItemSchema);
