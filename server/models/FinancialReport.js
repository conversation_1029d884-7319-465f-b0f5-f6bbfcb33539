import mongoose from 'mongoose';

const FinancialReportSchema = new mongoose.Schema({
  reportId: {
    type: String,
    required: true,
    unique: true,
    default: () => 'RPT' + Date.now().toString().slice(-8)
  },
  reportName: {
    type: String,
    required: true,
    trim: true
  },
  reportType: {
    type: String,
    required: true,
    enum: [
      'Revenue Report',
      'Payment Report', 
      'Outstanding Report',
      'Department Revenue',
      'Insurance Claims',
      'Daily Summary',
      'Monthly Summary',
      'Quarterly Summary',
      'Annual Summary',
      'Aging Report',
      'Tax Report',
      'Profit & Loss',
      'Cash Flow',
      'Custom Report'
    ]
  },
  reportPeriod: {
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    }
  },
  filters: {
    departments: [String],
    paymentStatus: [String],
    billTypes: [String],
    providers: [String],
    insuranceProviders: [String]
  },
  reportData: {
    summary: {
      totalRevenue: { type: Number, default: 0 },
      totalPaid: { type: Number, default: 0 },
      totalOutstanding: { type: Number, default: 0 },
      totalBills: { type: Number, default: 0 },
      averageBillAmount: { type: Number, default: 0 },
      collectionRate: { type: Number, default: 0 }
    },
    departmentBreakdown: [{
      department: String,
      revenue: Number,
      billCount: Number,
      percentage: Number
    }],
    paymentMethodBreakdown: [{
      method: String,
      amount: Number,
      count: Number,
      percentage: Number
    }],
    agingAnalysis: [{
      ageGroup: String, // 0-30, 31-60, 61-90, 90+
      amount: Number,
      count: Number,
      percentage: Number
    }],
    trends: [{
      period: String,
      revenue: Number,
      collections: Number,
      outstanding: Number
    }]
  },
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  generatedAt: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['Generating', 'Completed', 'Failed'],
    default: 'Generating'
  },
  fileUrl: {
    type: String,
    trim: true
  },
  fileSize: {
    type: Number
  },
  downloadCount: {
    type: Number,
    default: 0
  },
  lastDownloadedAt: Date,
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  sharedWith: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    permissions: {
      type: String,
      enum: ['View', 'Download', 'Edit'],
      default: 'View'
    },
    sharedAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Indexes
FinancialReportSchema.index({ reportId: 1 });
FinancialReportSchema.index({ reportType: 1 });
FinancialReportSchema.index({ generatedBy: 1 });
FinancialReportSchema.index({ generatedAt: -1 });
FinancialReportSchema.index({ 'reportPeriod.startDate': 1, 'reportPeriod.endDate': 1 });
FinancialReportSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Instance Methods
FinancialReportSchema.methods.incrementDownloadCount = function() {
  this.downloadCount += 1;
  this.lastDownloadedAt = new Date();
  return this.save();
};

FinancialReportSchema.methods.shareWith = function(userId, permissions = 'View') {
  const existingShare = this.sharedWith.find(share => 
    share.user.toString() === userId.toString()
  );
  
  if (existingShare) {
    existingShare.permissions = permissions;
    existingShare.sharedAt = new Date();
  } else {
    this.sharedWith.push({
      user: userId,
      permissions,
      sharedAt: new Date()
    });
  }
  
  return this.save();
};

// Static Methods
FinancialReportSchema.statics.getRecentReports = function(userId, limit = 10) {
  return this.find({
    $or: [
      { generatedBy: userId },
      { 'sharedWith.user': userId },
      { isPublic: true }
    ]
  })
  .sort({ generatedAt: -1 })
  .limit(limit)
  .populate('generatedBy', 'firstName lastName')
  .populate('sharedWith.user', 'firstName lastName');
};

FinancialReportSchema.statics.cleanupExpiredReports = function() {
  const now = new Date();
  return this.deleteMany({
    expiresAt: { $lt: now },
    status: 'Completed'
  });
};

export default mongoose.model('FinancialReport', FinancialReportSchema);
