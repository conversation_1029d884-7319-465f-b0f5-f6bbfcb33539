import mongoose from 'mongoose';

const LabTestSchema = new mongoose.Schema({
  testId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'LAB' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  testName: {
    type: String,
    required: true,
    trim: true
  },
  testCategory: {
    type: String,
    required: true,
    enum: [
      'Blood Test',
      'Urine Test',
      'Stool Test',
      'Imaging',
      'Biopsy',
      'Microbiology',
      'Biochemistry',
      'Hematology',
      'Immunology',
      'Pathology',
      'Genetics'
    ]
  },
  testCode: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  instructions: {
    type: String,
    trim: true
  },
  sampleType: {
    type: String,
    enum: ['Blood', 'Urine', 'Stool', 'Saliva', 'Tissue', 'Swab', 'Other'],
    required: true
  },
  priority: {
    type: String,
    enum: ['Routine', 'Urgent', 'STAT'],
    default: 'Routine'
  },
  status: {
    type: String,
    enum: ['Ordered', 'Sample Collected', 'In Progress', 'Completed', 'Cancelled'],
    default: 'Ordered'
  },
  orderDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  sampleCollectionDate: Date,
  resultDate: Date,
  expectedResultDate: Date,
  results: {
    values: [{
      parameter: { type: String, required: true },
      value: { type: String, required: true },
      unit: String,
      referenceRange: String,
      status: {
        type: String,
        enum: ['Normal', 'Abnormal', 'Critical', 'Pending']
      }
    }],
    interpretation: String,
    conclusion: String,
    recommendations: String
  },
  technician: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  cost: {
    type: Number,
    min: 0
  },
  notes: String
}, {
  timestamps: true
});

// Indexes
LabTestSchema.index({ testId: 1 });
LabTestSchema.index({ patient: 1 });
LabTestSchema.index({ doctor: 1 });
LabTestSchema.index({ status: 1 });
LabTestSchema.index({ testCategory: 1 });
LabTestSchema.index({ orderDate: 1 });
LabTestSchema.index({ priority: 1 });

export default mongoose.model('LabTest', LabTestSchema);
