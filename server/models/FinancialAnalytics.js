import mongoose from 'mongoose';

// Financial KPI Schema
const financialKPISchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  value: {
    type: Number,
    required: true
  },
  target: {
    type: Number
  },
  unit: {
    type: String,
    enum: ['currency', 'percentage', 'count', 'ratio', 'days'],
    default: 'currency'
  },
  trend: {
    type: String,
    enum: ['up', 'down', 'stable'],
    default: 'stable'
  },
  changePercent: {
    type: Number,
    default: 0
  },
  category: {
    type: String,
    enum: ['revenue', 'expense', 'profitability', 'efficiency', 'liquidity', 'activity'],
    required: true
  }
});

// Financial Analytics Schema
const FinancialAnalyticsSchema = new mongoose.Schema({
  analyticsId: {
    type: String,
    required: true,
    unique: true,
    default: () => 'FA' + Date.now().toString().slice(-8)
  },
  
  // Time Period
  period: {
    type: String,
    required: true,
    enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly']
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  
  // Revenue Analytics
  revenue: {
    total: {
      type: Number,
      default: 0
    },
    byDepartment: [{
      department: String,
      amount: Number,
      percentage: Number
    }],
    byCategory: [{
      category: String,
      amount: Number,
      percentage: Number
    }],
    byPaymentMethod: [{
      method: String,
      amount: Number,
      percentage: Number
    }],
    growth: {
      amount: Number,
      percentage: Number
    }
  },
  
  // Expense Analytics
  expenses: {
    total: {
      type: Number,
      default: 0
    },
    byCategory: [{
      category: String,
      amount: Number,
      percentage: Number
    }],
    byVendor: [{
      vendor: String,
      amount: Number,
      percentage: Number
    }],
    operational: Number,
    administrative: Number,
    clinical: Number
  },
  
  // Profitability Analytics
  profitability: {
    grossProfit: Number,
    netProfit: Number,
    grossMargin: Number,
    netMargin: Number,
    ebitda: Number,
    operatingRatio: Number
  },
  
  // Cash Flow Analytics
  cashFlow: {
    operating: Number,
    investing: Number,
    financing: Number,
    netCashFlow: Number,
    cashPosition: Number
  },
  
  // Accounts Receivable Analytics
  accountsReceivable: {
    total: Number,
    current: Number,
    overdue30: Number,
    overdue60: Number,
    overdue90: Number,
    overdue90Plus: Number,
    averageCollectionPeriod: Number,
    turnoverRatio: Number
  },
  
  // Key Performance Indicators
  kpis: [financialKPISchema],
  
  // Forecasting Data
  forecast: {
    nextPeriodRevenue: Number,
    nextPeriodExpenses: Number,
    nextPeriodProfit: Number,
    confidence: {
      type: Number,
      min: 0,
      max: 100
    },
    methodology: String
  },
  
  // Benchmarking
  benchmarks: {
    industryAverage: {
      revenue: Number,
      expenses: Number,
      profitMargin: Number
    },
    previousPeriod: {
      revenue: Number,
      expenses: Number,
      profitMargin: Number
    }
  },
  
  // Risk Analytics
  riskMetrics: {
    badDebtRatio: Number,
    concentrationRisk: Number,
    liquidityRatio: Number,
    debtToEquityRatio: Number,
    riskScore: {
      type: Number,
      min: 0,
      max: 100
    }
  },
  
  // Operational Metrics
  operationalMetrics: {
    averageBillAmount: Number,
    billsPerDay: Number,
    paymentCycleTime: Number,
    collectionEfficiency: Number,
    costPerPatient: Number,
    revenuePerBed: Number
  },
  
  // Data Quality Metrics
  dataQuality: {
    completeness: Number,
    accuracy: Number,
    timeliness: Number,
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  
  // Audit Information
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  generatedAt: {
    type: Date,
    default: Date.now
  },
  
  // Status
  status: {
    type: String,
    enum: ['generating', 'completed', 'error', 'archived'],
    default: 'generating'
  },
  
  // Metadata
  metadata: {
    calculationTime: Number,
    dataPoints: Number,
    version: {
      type: String,
      default: '1.0'
    }
  }
}, {
  timestamps: true
});

// Indexes for performance
FinancialAnalyticsSchema.index({ period: 1, startDate: 1, endDate: 1 });
FinancialAnalyticsSchema.index({ generatedAt: -1 });
FinancialAnalyticsSchema.index({ status: 1 });
FinancialAnalyticsSchema.index({ 'revenue.total': -1 });

// Instance Methods
FinancialAnalyticsSchema.methods.calculateGrowthRate = function(previousValue, currentValue) {
  if (previousValue === 0) return 0;
  return ((currentValue - previousValue) / previousValue) * 100;
};

FinancialAnalyticsSchema.methods.calculateMargin = function(revenue, cost) {
  if (revenue === 0) return 0;
  return ((revenue - cost) / revenue) * 100;
};

// Static Methods
FinancialAnalyticsSchema.statics.getLatestAnalytics = function(period) {
  return this.findOne({ period, status: 'completed' })
    .sort({ generatedAt: -1 });
};

FinancialAnalyticsSchema.statics.getTrendData = function(period, periods = 12) {
  return this.find({ period, status: 'completed' })
    .sort({ startDate: -1 })
    .limit(periods);
};

export default mongoose.model('FinancialAnalytics', FinancialAnalyticsSchema);
