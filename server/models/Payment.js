import mongoose from 'mongoose';

const PaymentSchema = new mongoose.Schema({
  paymentId: {
    type: String,
    required: true,
    unique: true,
    default: () => 'PAY' + Date.now().toString().slice(-8)
  },
  transactionNumber: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const timestamp = Date.now().toString().slice(-6);
      return `TXN${year}${month}${day}${timestamp}`;
    }
  },
  bill: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Bill',
    required: true,
    index: true
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: [
      'Cash',
      'Credit Card',
      'Debit Card', 
      'Bank Transfer',
      'Check',
      'Insurance',
      'Online Payment',
      'UPI',
      'Wallet',
      'EMI',
      'Corporate Payment',
      'Government Scheme'
    ]
  },
  paymentDate: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // Payment Details
  paymentDetails: {
    // Card Payment Details
    cardDetails: {
      last4Digits: String,
      cardType: { type: String, enum: ['Visa', 'MasterCard', 'Amex', 'Discover', 'RuPay'] },
      authCode: String,
      merchantId: String,
      terminalId: String
    },
    
    // Bank Transfer Details
    bankDetails: {
      bankName: String,
      accountNumber: String,
      ifscCode: String,
      utrNumber: String,
      beneficiaryName: String
    },
    
    // Check Details
    checkDetails: {
      checkNumber: String,
      bankName: String,
      checkDate: Date,
      drawerName: String
    },
    
    // Digital Payment Details
    digitalDetails: {
      upiId: String,
      walletProvider: String,
      walletTransactionId: String,
      qrCodeId: String
    },
    
    // Insurance Details
    insuranceDetails: {
      provider: String,
      policyNumber: String,
      claimNumber: String,
      approvalCode: String,
      copayAmount: Number,
      deductibleAmount: Number
    }
  },
  
  // Transaction Information
  transactionId: {
    type: String,
    trim: true,
    index: true
  },
  referenceNumber: {
    type: String,
    trim: true
  },
  gatewayTransactionId: {
    type: String,
    trim: true
  },
  
  // Status and Processing
  paymentStatus: {
    type: String,
    enum: ['Pending', 'Processing', 'Completed', 'Failed', 'Cancelled', 'Refunded', 'Partially Refunded'],
    default: 'Pending',
    index: true
  },
  processingFee: {
    type: Number,
    default: 0,
    min: 0
  },
  netAmount: {
    type: Number,
    required: true,
    min: 0
  },
  
  // Refund Information
  refunds: [{
    refundId: {
      type: String,
      default: () => 'REF' + Date.now().toString().slice(-8)
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    reason: {
      type: String,
      required: true,
      trim: true
    },
    refundDate: {
      type: Date,
      default: Date.now
    },
    refundMethod: {
      type: String,
      enum: ['Original Payment Method', 'Bank Transfer', 'Check', 'Cash'],
      default: 'Original Payment Method'
    },
    refundStatus: {
      type: String,
      enum: ['Pending', 'Processing', 'Completed', 'Failed'],
      default: 'Pending'
    },
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    gatewayRefundId: String,
    notes: String
  }],
  
  // Installment Information (for EMI payments)
  installment: {
    isInstallment: {
      type: Boolean,
      default: false
    },
    installmentNumber: Number,
    totalInstallments: Number,
    installmentAmount: Number,
    nextDueDate: Date,
    remainingAmount: Number
  },
  
  // Audit and Notes
  notes: {
    type: String,
    trim: true
  },
  internalNotes: {
    type: String,
    trim: true
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verifiedAt: Date,
  
  // Receipt Information
  receiptNumber: {
    type: String,
    unique: true,
    sparse: true
  },
  receiptGenerated: {
    type: Boolean,
    default: false
  },
  receiptUrl: String,
  
  // System Fields
  isActive: {
    type: Boolean,
    default: true
  },
  fiscalYear: {
    type: String,
    default: () => {
      const now = new Date();
      const year = now.getFullYear();
      return `${year}-${year + 1}`;
    }
  }
}, {
  timestamps: true
});

// Indexes
PaymentSchema.index({ paymentId: 1 });
PaymentSchema.index({ transactionNumber: 1 });
PaymentSchema.index({ bill: 1 });
PaymentSchema.index({ patient: 1 });
PaymentSchema.index({ paymentDate: -1 });
PaymentSchema.index({ paymentStatus: 1 });
PaymentSchema.index({ paymentMethod: 1 });
PaymentSchema.index({ fiscalYear: 1 });
PaymentSchema.index({ transactionId: 1 });
PaymentSchema.index({ receiptNumber: 1 });

// Compound Indexes
PaymentSchema.index({ patient: 1, paymentDate: -1 });
PaymentSchema.index({ paymentMethod: 1, paymentDate: -1 });
PaymentSchema.index({ paymentStatus: 1, paymentDate: -1 });

// Pre-save middleware
PaymentSchema.pre('save', function(next) {
  // Calculate net amount after processing fee
  this.netAmount = this.amount - (this.processingFee || 0);
  
  // Generate receipt number if payment is completed and receipt not generated
  if (this.paymentStatus === 'Completed' && !this.receiptNumber) {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const timestamp = Date.now().toString().slice(-6);
    this.receiptNumber = `RCP${year}${month}${timestamp}`;
  }
  
  next();
});

// Instance Methods
PaymentSchema.methods.addRefund = function(refundData) {
  this.refunds.push(refundData);
  
  const totalRefunded = this.refunds
    .filter(refund => refund.refundStatus === 'Completed')
    .reduce((sum, refund) => sum + refund.amount, 0);
  
  if (totalRefunded >= this.amount) {
    this.paymentStatus = 'Refunded';
  } else if (totalRefunded > 0) {
    this.paymentStatus = 'Partially Refunded';
  }
  
  return this.save();
};

PaymentSchema.methods.getTotalRefunded = function() {
  return this.refunds
    .filter(refund => refund.refundStatus === 'Completed')
    .reduce((sum, refund) => sum + refund.amount, 0);
};

PaymentSchema.methods.canRefund = function() {
  return this.paymentStatus === 'Completed' && 
         this.getTotalRefunded() < this.amount;
};

// Static Methods
PaymentSchema.statics.getPaymentsByDateRange = function(startDate, endDate) {
  return this.find({
    paymentDate: { $gte: startDate, $lte: endDate },
    paymentStatus: 'Completed',
    isActive: true
  }).populate('bill patient processedBy');
};

PaymentSchema.statics.getPaymentMethodStats = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        paymentDate: { $gte: startDate, $lte: endDate },
        paymentStatus: 'Completed',
        isActive: true
      }
    },
    {
      $group: {
        _id: '$paymentMethod',
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 },
        avgAmount: { $avg: '$amount' }
      }
    },
    { $sort: { totalAmount: -1 } }
  ]);
};

export default mongoose.model('Payment', PaymentSchema);
