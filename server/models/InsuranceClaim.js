import mongoose from 'mongoose';

const InsuranceClaimSchema = new mongoose.Schema({
  claimId: {
    type: String,
    required: true,
    unique: true,
    default: () => 'CLM' + Date.now().toString().slice(-8)
  },
  claimNumber: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const timestamp = Date.now().toString().slice(-6);
      return `INS${year}${month}${timestamp}`;
    }
  },
  
  // Related Documents
  bill: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Bill',
    required: true,
    index: true
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  
  // Insurance Information
  insuranceProvider: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    code: {
      type: String,
      trim: true
    },
    contactPerson: String,
    contactPhone: String,
    contactEmail: String,
    address: String
  },
  
  // Policy Details
  policyDetails: {
    policyNumber: {
      type: String,
      required: true,
      trim: true,
      index: true
    },
    policyHolderName: {
      type: String,
      required: true,
      trim: true
    },
    relationshipToPatient: {
      type: String,
      enum: ['Self', 'Spouse', 'Child', 'Parent', 'Sibling', 'Other'],
      default: 'Self'
    },
    policyType: {
      type: String,
      enum: ['Individual', 'Family', 'Group', 'Corporate'],
      default: 'Individual'
    },
    coverageAmount: {
      type: Number,
      required: true,
      min: 0
    },
    deductible: {
      type: Number,
      default: 0,
      min: 0
    },
    copayPercentage: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    policyStartDate: Date,
    policyEndDate: Date,
    isActive: {
      type: Boolean,
      default: true
    }
  },
  
  // Claim Financial Details
  claimAmount: {
    type: Number,
    required: true,
    min: 0
  },
  approvedAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  rejectedAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  deductibleAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  copayAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  netPayableAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // Pre-authorization
  preAuthorization: {
    isRequired: {
      type: Boolean,
      default: false
    },
    preAuthNumber: String,
    preAuthAmount: Number,
    preAuthDate: Date,
    preAuthExpiryDate: Date,
    preAuthStatus: {
      type: String,
      enum: ['Pending', 'Approved', 'Rejected', 'Expired'],
      default: 'Pending'
    },
    preAuthNotes: String
  },
  
  // Claim Status and Processing
  claimStatus: {
    type: String,
    enum: [
      'Draft',
      'Submitted',
      'Under Review',
      'Additional Info Required',
      'Approved',
      'Partially Approved',
      'Rejected',
      'Paid',
      'Closed'
    ],
    default: 'Draft',
    index: true
  },
  
  // Important Dates
  serviceDate: {
    type: Date,
    required: true
  },
  submissionDate: Date,
  reviewStartDate: Date,
  approvalDate: Date,
  paymentDate: Date,
  
  // Processing Information
  processingNotes: [{
    note: {
      type: String,
      required: true,
      trim: true
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    },
    isInternal: {
      type: Boolean,
      default: false
    }
  }],
  
  // Rejection/Denial Information
  rejectionDetails: {
    rejectionReason: String,
    rejectionCode: String,
    rejectionDate: Date,
    canAppeal: {
      type: Boolean,
      default: true
    },
    appealDeadline: Date
  },
  
  // Documents and Attachments
  documents: [{
    documentType: {
      type: String,
      enum: [
        'Medical Records',
        'Lab Reports',
        'Imaging Reports',
        'Prescription',
        'Discharge Summary',
        'Invoice',
        'Pre-auth Form',
        'Claim Form',
        'ID Proof',
        'Policy Document',
        'Other'
      ],
      required: true
    },
    fileName: String,
    fileUrl: String,
    fileSize: Number,
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    },
    isRequired: {
      type: Boolean,
      default: false
    },
    isVerified: {
      type: Boolean,
      default: false
    }
  }],
  
  // Communication Log
  communications: [{
    communicationType: {
      type: String,
      enum: ['Phone', 'Email', 'Letter', 'Fax', 'Portal', 'In-Person'],
      required: true
    },
    direction: {
      type: String,
      enum: ['Incoming', 'Outgoing'],
      required: true
    },
    subject: String,
    content: String,
    contactPerson: String,
    communicationDate: {
      type: Date,
      default: Date.now
    },
    handledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    followUpRequired: {
      type: Boolean,
      default: false
    },
    followUpDate: Date
  }],
  
  // System Fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Priority and SLA
  priority: {
    type: String,
    enum: ['Low', 'Normal', 'High', 'Urgent'],
    default: 'Normal'
  },
  slaDeadline: Date,
  
  // Audit Trail
  statusHistory: [{
    status: String,
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    changedAt: {
      type: Date,
      default: Date.now
    },
    reason: String
  }],
  
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes
InsuranceClaimSchema.index({ claimId: 1 });
InsuranceClaimSchema.index({ claimNumber: 1 });
InsuranceClaimSchema.index({ bill: 1 });
InsuranceClaimSchema.index({ patient: 1 });
InsuranceClaimSchema.index({ claimStatus: 1 });
InsuranceClaimSchema.index({ 'policyDetails.policyNumber': 1 });
InsuranceClaimSchema.index({ 'insuranceProvider.name': 1 });
InsuranceClaimSchema.index({ serviceDate: -1 });
InsuranceClaimSchema.index({ submissionDate: -1 });
InsuranceClaimSchema.index({ assignedTo: 1 });
InsuranceClaimSchema.index({ priority: 1 });

// Compound Indexes
InsuranceClaimSchema.index({ claimStatus: 1, priority: 1 });
InsuranceClaimSchema.index({ assignedTo: 1, claimStatus: 1 });

// Pre-save middleware
InsuranceClaimSchema.pre('save', function(next) {
  // Calculate net payable amount
  this.netPayableAmount = this.approvedAmount - this.deductibleAmount - this.copayAmount;
  
  // Add to status history if status changed
  if (this.isModified('claimStatus')) {
    this.statusHistory.push({
      status: this.claimStatus,
      changedBy: this.lastModifiedBy,
      changedAt: new Date()
    });
  }
  
  next();
});

// Instance Methods
InsuranceClaimSchema.methods.addNote = function(note, userId, isInternal = false) {
  this.processingNotes.push({
    note,
    addedBy: userId,
    addedAt: new Date(),
    isInternal
  });
  return this.save();
};

InsuranceClaimSchema.methods.addCommunication = function(commData) {
  this.communications.push(commData);
  return this.save();
};

InsuranceClaimSchema.methods.updateStatus = function(newStatus, userId, reason) {
  this.claimStatus = newStatus;
  this.lastModifiedBy = userId;
  
  this.statusHistory.push({
    status: newStatus,
    changedBy: userId,
    changedAt: new Date(),
    reason
  });
  
  return this.save();
};

InsuranceClaimSchema.methods.isOverdue = function() {
  return this.slaDeadline && new Date() > this.slaDeadline;
};

// Static Methods
InsuranceClaimSchema.statics.getClaimsByStatus = function(status) {
  return this.find({ claimStatus: status, isActive: true })
    .populate('bill patient assignedTo')
    .sort({ createdAt: -1 });
};

InsuranceClaimSchema.statics.getOverdueClaims = function() {
  const now = new Date();
  return this.find({
    slaDeadline: { $lt: now },
    claimStatus: { $nin: ['Paid', 'Closed', 'Rejected'] },
    isActive: true
  });
};

export default mongoose.model('InsuranceClaim', InsuranceClaimSchema);
