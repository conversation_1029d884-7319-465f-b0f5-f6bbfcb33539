import mongoose, { Schema, Document } from 'mongoose';

export interface INotification extends Document {
  recipient: mongoose.Types.ObjectId;
  sender?: mongoose.Types.ObjectId;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success' | 'appointment' | 'payment' | 'system';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'general' | 'appointment' | 'payment' | 'system' | 'medical' | 'billing';
  data?: any;
  actionUrl?: string;
  isRead: boolean;
  isActive: boolean;
  expiresAt?: Date;
  markAsRead(): Promise<INotification>;
}

interface INotificationModel extends mongoose.Model<INotification> {
  createNotification(data: any): Promise<INotification>;
  getUnreadCount(userId: string): Promise<number>;
  markAllAsRead(userId: string): Promise<void>;
}

const NotificationSchema: Schema = new Schema({
  recipient: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sender: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255
  },
  message: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  type: {
    type: String,
    enum: ['info', 'warning', 'error', 'success', 'appointment', 'payment', 'system'],
    default: 'info'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  category: {
    type: String,
    enum: ['general', 'appointment', 'payment', 'system', 'medical', 'billing'],
    default: 'general'
  },
  data: {
    type: Schema.Types.Mixed,
    default: {}
  },
  actionUrl: {
    type: String,
    trim: true
  },
  isRead: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  expiresAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Index for better query performance
NotificationSchema.index({ recipient: 1, isRead: 1, isActive: 1 });
NotificationSchema.index({ recipient: 1, createdAt: -1 });
NotificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Instance method to mark notification as read
NotificationSchema.methods.markAsRead = async function(): Promise<INotification> {
  this.isRead = true;
  return this.save();
};

// Static method to create a notification
NotificationSchema.statics.createNotification = async function(data: any): Promise<INotification> {
  const notification = new this(data);
  await notification.save();
  return notification.populate('sender', 'firstName lastName role');
};

// Static method to get unread count for a user
NotificationSchema.statics.getUnreadCount = async function(userId: string): Promise<number> {
  return this.countDocuments({
    recipient: userId,
    isRead: false,
    isActive: true
  });
};

// Static method to mark all notifications as read for a user
NotificationSchema.statics.markAllAsRead = async function(userId: string): Promise<void> {
  await this.updateMany(
    { recipient: userId, isRead: false, isActive: true },
    { isRead: true }
  );
};

export default mongoose.model<INotification, INotificationModel>('Notification', NotificationSchema);
