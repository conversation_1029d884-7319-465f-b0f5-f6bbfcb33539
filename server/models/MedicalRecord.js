import mongoose from 'mongoose';

const MedicalRecordSchema = new mongoose.Schema({
  recordId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'MR' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  visitDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  visitType: {
    type: String,
    enum: ['Consultation', 'Follow-up', 'Emergency', 'Routine Check-up', 'Procedure'],
    required: true
  },
  chiefComplaint: {
    type: String,
    required: true,
    trim: true
  },
  historyOfPresentIllness: {
    type: String,
    trim: true
  },
  pastMedicalHistory: {
    type: String,
    trim: true
  },
  familyHistory: {
    type: String,
    trim: true
  },
  socialHistory: {
    type: String,
    trim: true
  },
  allergies: [{
    allergen: { type: String, required: true },
    reaction: { type: String, required: true },
    severity: {
      type: String,
      enum: ['Mild', 'Moderate', 'Severe'],
      required: true
    }
  }],
  currentMedications: [{
    medication: { type: String, required: true },
    dosage: { type: String, required: true },
    frequency: { type: String, required: true },
    startDate: Date,
    endDate: Date
  }],
  vitalSigns: {
    temperature: { type: Number, min: 90, max: 110 }, // Fahrenheit
    bloodPressure: {
      systolic: { type: Number, min: 60, max: 250 },
      diastolic: { type: Number, min: 40, max: 150 }
    },
    heartRate: { type: Number, min: 40, max: 200 },
    respiratoryRate: { type: Number, min: 8, max: 40 },
    oxygenSaturation: { type: Number, min: 70, max: 100 },
    weight: { type: Number, min: 0 }, // kg
    height: { type: Number, min: 0 }, // cm
    bmi: Number
  },
  physicalExamination: {
    general: String,
    head: String,
    neck: String,
    chest: String,
    cardiovascular: String,
    respiratory: String,
    abdomen: String,
    extremities: String,
    neurological: String,
    skin: String
  },
  diagnosis: {
    primary: { type: String, required: true },
    secondary: [String],
    differential: [String]
  },
  treatmentPlan: {
    type: String,
    required: true,
    trim: true
  },
  prescriptions: [{
    medication: { type: String, required: true },
    dosage: { type: String, required: true },
    frequency: { type: String, required: true },
    duration: { type: String, required: true },
    instructions: String
  }],
  labTests: [{
    test: { type: String, required: true },
    ordered: { type: Boolean, default: true },
    completed: { type: Boolean, default: false },
    results: String
  }],
  procedures: [{
    procedure: { type: String, required: true },
    performed: { type: Boolean, default: false },
    date: Date,
    notes: String
  }],
  followUpInstructions: {
    type: String,
    trim: true
  },
  nextAppointment: Date,
  status: {
    type: String,
    enum: ['Active', 'Completed', 'Cancelled'],
    default: 'Active'
  },
  outcome: {
    type: String,
    enum: ['Improved', 'Stable', 'Worsened', 'Resolved', 'Referred', 'Discharged']
  },
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Indexes
MedicalRecordSchema.index({ recordId: 1 });
MedicalRecordSchema.index({ patient: 1 });
MedicalRecordSchema.index({ doctor: 1 });
MedicalRecordSchema.index({ visitDate: 1 });
MedicalRecordSchema.index({ status: 1 });

// Pre-save middleware to calculate BMI
MedicalRecordSchema.pre('save', function(next) {
  if (this.vitalSigns.weight && this.vitalSigns.height) {
    const heightInMeters = this.vitalSigns.height / 100;
    this.vitalSigns.bmi = this.vitalSigns.weight / (heightInMeters * heightInMeters);
  }
  next();
});

export default mongoose.model('MedicalRecord', MedicalRecordSchema);
