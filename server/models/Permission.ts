import mongoose, { Schema, Document } from 'mongoose';

export interface IPermission extends Document {
  module: string;
  action: string;
  resource: string;
  description: string;
  isSystemPermission: boolean;
}

const PermissionSchema: Schema = new Schema({
  module: {
    type: String,
    required: true,
    enum: [
      'dashboard',
      'patients',
      'clinical',
      'laboratory',
      'pharmacy',
      'financial',
      'hr',
      'facility',
      'admin',
      'reports'
    ]
  },
  action: {
    type: String,
    required: true,
    enum: ['view', 'create', 'edit', 'delete', 'export', 'approve', 'assign']
  },
  resource: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  isSystemPermission: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Compound index for efficient queries
PermissionSchema.index({ module: 1, action: 1, resource: 1 }, { unique: true });

export default mongoose.model<IPermission>('Permission', PermissionSchema);