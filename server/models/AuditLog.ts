import mongoose, { Schema, Document } from 'mongoose';

export interface IAuditLog extends Document {
  user: mongoose.Types.ObjectId;
  action: string;
  resource?: string;
  resourceId?: string;
  details?: string;
  ipAddress: string;
  userAgent?: string;
  status: 'Success' | 'Failed' | 'Warning';
  timestamp: Date;
  metadata?: any;
}

const AuditLogSchema: Schema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  action: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255
  },
  resource: {
    type: String,
    trim: true,
    maxlength: 100
  },
  resourceId: {
    type: String,
    trim: true,
    maxlength: 100
  },
  details: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  ipAddress: {
    type: String,
    required: true,
    trim: true
  },
  userAgent: {
    type: String,
    trim: true,
    maxlength: 500
  },
  status: {
    type: String,
    enum: ['Success', 'Failed', 'Warning'],
    default: 'Success'
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Index for better query performance
AuditLogSchema.index({ user: 1, timestamp: -1 });
AuditLogSchema.index({ action: 1, timestamp: -1 });
AuditLogSchema.index({ timestamp: -1 });
AuditLogSchema.index({ status: 1, timestamp: -1 });

// Static method to create audit log entry
AuditLogSchema.statics.createLog = async function(logData: any) {
  const auditLog = new this(logData);
  return auditLog.save();
};

// Static method to get logs for a user
AuditLogSchema.statics.getLogsForUser = async function(userId: string, limit: number = 50) {
  return this.find({ user: userId })
    .populate('user', 'firstName lastName email')
    .sort({ timestamp: -1 })
    .limit(limit);
};

// Static method to get recent activity
AuditLogSchema.statics.getRecentActivity = async function(hours: number = 24) {
  const startTime = new Date();
  startTime.setHours(startTime.getHours() - hours);
  
  return this.find({ timestamp: { $gte: startTime } })
    .populate('user', 'firstName lastName email')
    .sort({ timestamp: -1 });
};

export default mongoose.model<IAuditLog>('AuditLog', AuditLogSchema);
