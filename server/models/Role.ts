import mongoose, { Schema, Document } from 'mongoose';

export interface IRole extends Document {
  name: string;
  description: string;
  defaultPermissions: mongoose.Types.ObjectId[];
  level: number;
  isSystemRole: boolean;
}

const RoleSchema: Schema = new Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  defaultPermissions: [{
    type: Schema.Types.ObjectId,
    ref: 'Permission'
  }],
  level: {
    type: Number,
    required: true,
    min: 1,
    max: 10
  },
  isSystemRole: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

export default mongoose.model<IRole>('Role', RoleSchema);