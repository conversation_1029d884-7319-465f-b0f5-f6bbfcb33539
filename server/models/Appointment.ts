import mongoose, { Schema, Document } from 'mongoose';

export interface IAppointment extends Document {
  appointmentId: string;
  patient: mongoose.Types.ObjectId;
  doctor: mongoose.Types.ObjectId;
  department: string;
  appointmentDate: Date;
  appointmentTime: string;
  duration: number; // in minutes
  type: 'Consultation' | 'Follow-up' | 'Emergency' | 'Surgery' | 'Diagnostic';
  status: 'Scheduled' | 'Confirmed' | 'In Progress' | 'Completed' | 'Cancelled' | 'No Show';
  priority: 'Low' | 'Medium' | 'High' | 'Emergency';
  reason: string;
  symptoms?: string[];
  notes?: string;
  diagnosis?: string;
  prescription?: Array<{
    medication: string;
    dosage: string;
    frequency: string;
    duration: string;
    instructions?: string;
  }>;
  followUpDate?: Date;
  room?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}

const AppointmentSchema: Schema = new Schema({
  appointmentId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'APT' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  department: {
    type: String,
    required: true,
    enum: [
      'Cardiology',
      'Neurology',
      'Orthopedics',
      'Pediatrics',
      'Gynecology',
      'Dermatology',
      'Psychiatry',
      'Oncology',
      'Emergency',
      'General Medicine',
      'Surgery',
      'Radiology',
      'Pathology'
    ]
  },
  appointmentDate: {
    type: Date,
    required: true
  },
  appointmentTime: {
    type: String,
    required: true
  },
  duration: {
    type: Number,
    default: 30,
    min: 15,
    max: 240
  },
  type: {
    type: String,
    required: true,
    enum: ['Consultation', 'Follow-up', 'Emergency', 'Surgery', 'Diagnostic']
  },
  status: {
    type: String,
    required: true,
    enum: ['Scheduled', 'Confirmed', 'In Progress', 'Completed', 'Cancelled', 'No Show'],
    default: 'Scheduled'
  },
  priority: {
    type: String,
    required: true,
    enum: ['Low', 'Medium', 'High', 'Emergency'],
    default: 'Medium'
  },
  reason: {
    type: String,
    required: true,
    trim: true
  },
  symptoms: [String],
  notes: {
    type: String,
    trim: true
  },
  diagnosis: {
    type: String,
    trim: true
  },
  prescription: [{
    medication: { type: String, required: true },
    dosage: { type: String, required: true },
    frequency: { type: String, required: true },
    duration: { type: String, required: true },
    instructions: String
  }],
  followUpDate: Date,
  room: String,
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better performance
AppointmentSchema.index({ appointmentId: 1 });
AppointmentSchema.index({ patient: 1 });
AppointmentSchema.index({ doctor: 1 });
AppointmentSchema.index({ appointmentDate: 1 });
AppointmentSchema.index({ status: 1 });
AppointmentSchema.index({ department: 1 });

// Compound indexes
AppointmentSchema.index({ doctor: 1, appointmentDate: 1, appointmentTime: 1 });
AppointmentSchema.index({ patient: 1, appointmentDate: 1 });

// Pre-save middleware to update the updatedBy field
AppointmentSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedBy = this.createdBy; // This should be set by the controller
  }
  next();
});

export default mongoose.model<IAppointment>('Appointment', AppointmentSchema);
