import htmlPdf from 'html-pdf-node';
import fs from 'fs';
import path from 'path';

class PDFService {
  constructor() {
    this.options = {
      format: 'A4',
      border: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in'
      },
      type: 'pdf',
      quality: '75'
    };
  }

  // Generate Hospital Bill PDF
  async generateBill(billData) {
    const html = this.generateBillHTML(billData);
    const file = { content: html };
    
    try {
      const pdfBuffer = await htmlPdf.generatePdf(file, this.options);
      return pdfBuffer;
    } catch (error) {
      console.error('Error generating bill PDF:', error);
      throw new Error('Failed to generate bill PDF');
    }
  }

  // Generate Prescription PDF
  async generatePrescription(prescriptionData) {
    const html = this.generatePrescriptionHTML(prescriptionData);
    const file = { content: html };
    
    try {
      const pdfBuffer = await htmlPdf.generatePdf(file, this.options);
      return pdfBuffer;
    } catch (error) {
      console.error('Error generating prescription PDF:', error);
      throw new Error('Failed to generate prescription PDF');
    }
  }

  // Generate Medical Report PDF
  async generateMedicalReport(reportData) {
    const html = this.generateMedicalReportHTML(reportData);
    const file = { content: html };
    
    try {
      const pdfBuffer = await htmlPdf.generatePdf(file, this.options);
      return pdfBuffer;
    } catch (error) {
      console.error('Error generating medical report PDF:', error);
      throw new Error('Failed to generate medical report PDF');
    }
  }

  // Generate Inventory Report PDF
  async generateInventoryReport(inventoryData) {
    const html = this.generateInventoryReportHTML(inventoryData);
    const file = { content: html };
    
    try {
      const pdfBuffer = await htmlPdf.generatePdf(file, this.options);
      return pdfBuffer;
    } catch (error) {
      console.error('Error generating inventory report PDF:', error);
      throw new Error('Failed to generate inventory report PDF');
    }
  }

  // Generate Bill HTML Template
  generateBillHTML(billData) {
    const {
      billNumber,
      patientName,
      patientId,
      date,
      items,
      subtotal,
      tax,
      discount,
      total,
      paymentMethod,
      hospitalInfo
    } = billData;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Medical Bill - ${billNumber}</title>
      <style>
        body {
          font-family: 'Arial', sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
          line-height: 1.6;
        }
        .header {
          text-align: center;
          border-bottom: 3px solid #2563eb;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .hospital-name {
          font-size: 28px;
          font-weight: bold;
          color: #2563eb;
          margin-bottom: 5px;
        }
        .hospital-details {
          font-size: 14px;
          color: #666;
        }
        .bill-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .bill-details, .patient-details {
          width: 48%;
        }
        .section-title {
          font-size: 16px;
          font-weight: bold;
          color: #2563eb;
          margin-bottom: 10px;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }
        .items-table th,
        .items-table td {
          border: 1px solid #e5e7eb;
          padding: 12px;
          text-align: left;
        }
        .items-table th {
          background-color: #f8fafc;
          font-weight: bold;
          color: #374151;
        }
        .items-table tr:nth-child(even) {
          background-color: #f9fafb;
        }
        .total-section {
          margin-top: 30px;
          border-top: 2px solid #e5e7eb;
          padding-top: 20px;
        }
        .total-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 14px;
        }
        .grand-total {
          font-size: 18px;
          font-weight: bold;
          color: #2563eb;
          border-top: 1px solid #e5e7eb;
          padding-top: 10px;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          font-size: 12px;
          color: #666;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }
        .text-right {
          text-align: right;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="hospital-name">${hospitalInfo?.name || 'Hospital Management System'}</div>
        <div class="hospital-details">
          ${hospitalInfo?.address || 'Hospital Address'}<br>
          Phone: ${hospitalInfo?.phone || '+****************'} | Email: ${hospitalInfo?.email || '<EMAIL>'}
        </div>
      </div>

      <div class="bill-info">
        <div class="bill-details">
          <div class="section-title">Bill Information</div>
          <div class="detail-row">
            <span>Bill Number:</span>
            <span><strong>${billNumber}</strong></span>
          </div>
          <div class="detail-row">
            <span>Date:</span>
            <span>${new Date(date).toLocaleDateString()}</span>
          </div>
          <div class="detail-row">
            <span>Payment Method:</span>
            <span>${paymentMethod || 'Cash'}</span>
          </div>
        </div>
        
        <div class="patient-details">
          <div class="section-title">Patient Information</div>
          <div class="detail-row">
            <span>Patient Name:</span>
            <span><strong>${patientName}</strong></span>
          </div>
          <div class="detail-row">
            <span>Patient ID:</span>
            <span>${patientId}</span>
          </div>
        </div>
      </div>

      <table class="items-table">
        <thead>
          <tr>
            <th>Item</th>
            <th>Quantity</th>
            <th>Unit Price</th>
            <th class="text-right">Total</th>
          </tr>
        </thead>
        <tbody>
          ${items.map(item => `
            <tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>$${item.unitPrice.toFixed(2)}</td>
              <td class="text-right">$${(item.quantity * item.unitPrice).toFixed(2)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="total-section">
        <div class="total-row">
          <span>Subtotal:</span>
          <span>$${subtotal.toFixed(2)}</span>
        </div>
        ${discount > 0 ? `
        <div class="total-row">
          <span>Discount:</span>
          <span>-$${discount.toFixed(2)}</span>
        </div>
        ` : ''}
        <div class="total-row">
          <span>Tax:</span>
          <span>$${tax.toFixed(2)}</span>
        </div>
        <div class="total-row grand-total">
          <span>Grand Total:</span>
          <span>$${total.toFixed(2)}</span>
        </div>
      </div>

      <div class="footer">
        <p>Thank you for choosing our services!</p>
        <p>This is a computer-generated bill and does not require a signature.</p>
        <p>Generated on ${new Date().toLocaleString()}</p>
      </div>
    </body>
    </html>
    `;
  }

  // Generate Prescription HTML Template
  generatePrescriptionHTML(prescriptionData) {
    const {
      prescriptionId,
      patientName,
      patientAge,
      patientGender,
      doctorName,
      doctorSpecialty,
      date,
      medications,
      diagnosis,
      instructions,
      hospitalInfo
    } = prescriptionData;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Prescription - ${prescriptionId}</title>
      <style>
        body {
          font-family: 'Arial', sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
          line-height: 1.6;
        }
        .header {
          text-align: center;
          border-bottom: 3px solid #059669;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .hospital-name {
          font-size: 28px;
          font-weight: bold;
          color: #059669;
          margin-bottom: 5px;
        }
        .hospital-details {
          font-size: 14px;
          color: #666;
        }
        .prescription-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .patient-details, .doctor-details {
          width: 48%;
        }
        .section-title {
          font-size: 16px;
          font-weight: bold;
          color: #059669;
          margin-bottom: 10px;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .medications-section {
          margin: 30px 0;
        }
        .medication-item {
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
          background-color: #f9fafb;
        }
        .medication-name {
          font-size: 18px;
          font-weight: bold;
          color: #059669;
          margin-bottom: 8px;
        }
        .medication-details {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
          font-size: 14px;
        }
        .diagnosis-section, .instructions-section {
          margin: 20px 0;
          padding: 15px;
          background-color: #f0f9ff;
          border-left: 4px solid #0ea5e9;
          border-radius: 4px;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          font-size: 12px;
          color: #666;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="hospital-name">${hospitalInfo?.name || 'Hospital Management System'}</div>
        <div class="hospital-details">
          ${hospitalInfo?.address || 'Hospital Address'}<br>
          Phone: ${hospitalInfo?.phone || '+****************'} | Email: ${hospitalInfo?.email || '<EMAIL>'}
        </div>
      </div>

      <div class="prescription-info">
        <div class="patient-details">
          <div class="section-title">Patient Information</div>
          <div class="detail-row">
            <span>Name:</span>
            <span><strong>${patientName}</strong></span>
          </div>
          <div class="detail-row">
            <span>Age:</span>
            <span>${patientAge} years</span>
          </div>
          <div class="detail-row">
            <span>Gender:</span>
            <span>${patientGender}</span>
          </div>
        </div>
        
        <div class="doctor-details">
          <div class="section-title">Doctor Information</div>
          <div class="detail-row">
            <span>Doctor:</span>
            <span><strong>Dr. ${doctorName}</strong></span>
          </div>
          <div class="detail-row">
            <span>Specialty:</span>
            <span>${doctorSpecialty}</span>
          </div>
          <div class="detail-row">
            <span>Date:</span>
            <span>${new Date(date).toLocaleDateString()}</span>
          </div>
          <div class="detail-row">
            <span>Prescription ID:</span>
            <span>${prescriptionId}</span>
          </div>
        </div>
      </div>

      ${diagnosis ? `
      <div class="diagnosis-section">
        <div class="section-title">Diagnosis</div>
        <p>${diagnosis}</p>
      </div>
      ` : ''}

      <div class="medications-section">
        <div class="section-title">Prescribed Medications</div>
        ${medications.map(med => `
          <div class="medication-item">
            <div class="medication-name">${med.name}</div>
            <div class="medication-details">
              <div><strong>Dosage:</strong> ${med.dosage}</div>
              <div><strong>Frequency:</strong> ${med.frequency}</div>
              <div><strong>Duration:</strong> ${med.duration}</div>
              <div><strong>Instructions:</strong> ${med.instructions}</div>
            </div>
          </div>
        `).join('')}
      </div>

      ${instructions ? `
      <div class="instructions-section">
        <div class="section-title">Additional Instructions</div>
        <p>${instructions}</p>
      </div>
      ` : ''}

      <div class="footer">
        <p><strong>Doctor's Signature: ________________________</strong></p>
        <p>This prescription is valid for 30 days from the date of issue.</p>
        <p>Generated on ${new Date().toLocaleString()}</p>
      </div>
    </body>
    </html>
    `;
  }

  // Generate Medical Report HTML Template
  generateMedicalReportHTML(reportData) {
    const {
      reportId,
      patientName,
      patientId,
      doctorName,
      date,
      reportType,
      findings,
      recommendations,
      hospitalInfo
    } = reportData;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Medical Report - ${reportId}</title>
      <style>
        body {
          font-family: 'Arial', sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
          line-height: 1.6;
        }
        .header {
          text-align: center;
          border-bottom: 3px solid #dc2626;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .hospital-name {
          font-size: 28px;
          font-weight: bold;
          color: #dc2626;
          margin-bottom: 5px;
        }
        .hospital-details {
          font-size: 14px;
          color: #666;
        }
        .report-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .section-title {
          font-size: 16px;
          font-weight: bold;
          color: #dc2626;
          margin-bottom: 10px;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .content-section {
          margin: 20px 0;
          padding: 15px;
          background-color: #fef2f2;
          border-left: 4px solid #dc2626;
          border-radius: 4px;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          font-size: 12px;
          color: #666;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="hospital-name">${hospitalInfo?.name || 'Hospital Management System'}</div>
        <div class="hospital-details">
          ${hospitalInfo?.address || 'Hospital Address'}<br>
          Phone: ${hospitalInfo?.phone || '+****************'} | Email: ${hospitalInfo?.email || '<EMAIL>'}
        </div>
      </div>

      <div class="report-info">
        <div style="width: 48%;">
          <div class="section-title">Report Information</div>
          <div class="detail-row">
            <span>Report ID:</span>
            <span><strong>${reportId}</strong></span>
          </div>
          <div class="detail-row">
            <span>Report Type:</span>
            <span>${reportType}</span>
          </div>
          <div class="detail-row">
            <span>Date:</span>
            <span>${new Date(date).toLocaleDateString()}</span>
          </div>
          <div class="detail-row">
            <span>Doctor:</span>
            <span>Dr. ${doctorName}</span>
          </div>
        </div>

        <div style="width: 48%;">
          <div class="section-title">Patient Information</div>
          <div class="detail-row">
            <span>Patient Name:</span>
            <span><strong>${patientName}</strong></span>
          </div>
          <div class="detail-row">
            <span>Patient ID:</span>
            <span>${patientId}</span>
          </div>
        </div>
      </div>

      <div class="content-section">
        <div class="section-title">Findings</div>
        <p>${findings}</p>
      </div>

      <div class="content-section">
        <div class="section-title">Recommendations</div>
        <p>${recommendations}</p>
      </div>

      <div class="footer">
        <p><strong>Doctor's Signature: ________________________</strong></p>
        <p>This report is confidential and intended for medical use only.</p>
        <p>Generated on ${new Date().toLocaleString()}</p>
      </div>
    </body>
    </html>
    `;
  }

  // Generate Inventory Report HTML Template
  generateInventoryReportHTML(inventoryData) {
    const {
      reportDate,
      items,
      totalValue,
      lowStockItems,
      expiringItems,
      hospitalInfo
    } = inventoryData;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Inventory Report - ${new Date(reportDate).toLocaleDateString()}</title>
      <style>
        body {
          font-family: 'Arial', sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
          line-height: 1.6;
        }
        .header {
          text-align: center;
          border-bottom: 3px solid #7c3aed;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .hospital-name {
          font-size: 28px;
          font-weight: bold;
          color: #7c3aed;
          margin-bottom: 5px;
        }
        .hospital-details {
          font-size: 14px;
          color: #666;
        }
        .summary-section {
          display: flex;
          justify-content: space-around;
          margin-bottom: 30px;
          text-align: center;
        }
        .summary-item {
          padding: 15px;
          background-color: #f8fafc;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
        }
        .summary-value {
          font-size: 24px;
          font-weight: bold;
          color: #7c3aed;
        }
        .summary-label {
          font-size: 14px;
          color: #666;
        }
        .section-title {
          font-size: 16px;
          font-weight: bold;
          color: #7c3aed;
          margin-bottom: 10px;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }
        .items-table th,
        .items-table td {
          border: 1px solid #e5e7eb;
          padding: 8px;
          text-align: left;
          font-size: 12px;
        }
        .items-table th {
          background-color: #f8fafc;
          font-weight: bold;
          color: #374151;
        }
        .items-table tr:nth-child(even) {
          background-color: #f9fafb;
        }
        .alert-section {
          margin: 20px 0;
          padding: 15px;
          background-color: #fef3c7;
          border-left: 4px solid #f59e0b;
          border-radius: 4px;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          font-size: 12px;
          color: #666;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }
        .text-right {
          text-align: right;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="hospital-name">${hospitalInfo?.name || 'Hospital Management System'}</div>
        <div class="hospital-details">
          ${hospitalInfo?.address || 'Hospital Address'}<br>
          Phone: ${hospitalInfo?.phone || '+****************'} | Email: ${hospitalInfo?.email || '<EMAIL>'}
        </div>
        <h2>Inventory Report - ${new Date(reportDate).toLocaleDateString()}</h2>
      </div>

      <div class="summary-section">
        <div class="summary-item">
          <div class="summary-value">${items.length}</div>
          <div class="summary-label">Total Items</div>
        </div>
        <div class="summary-item">
          <div class="summary-value">$${totalValue.toFixed(2)}</div>
          <div class="summary-label">Total Value</div>
        </div>
        <div class="summary-item">
          <div class="summary-value">${lowStockItems.length}</div>
          <div class="summary-label">Low Stock Items</div>
        </div>
        <div class="summary-item">
          <div class="summary-value">${expiringItems.length}</div>
          <div class="summary-label">Expiring Soon</div>
        </div>
      </div>

      <div class="section-title">Inventory Items</div>
      <table class="items-table">
        <thead>
          <tr>
            <th>Item Name</th>
            <th>Category</th>
            <th>Current Stock</th>
            <th>Reorder Level</th>
            <th>Unit Price</th>
            <th>Total Value</th>
            <th>Expiry Date</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          ${items.map(item => `
            <tr>
              <td>${item.name}</td>
              <td>${item.category}</td>
              <td>${item.currentStock}</td>
              <td>${item.reorderLevel}</td>
              <td>$${item.unitPrice.toFixed(2)}</td>
              <td class="text-right">$${(item.currentStock * item.unitPrice).toFixed(2)}</td>
              <td>${new Date(item.expiryDate).toLocaleDateString()}</td>
              <td>${item.status}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      ${lowStockItems.length > 0 ? `
      <div class="alert-section">
        <div class="section-title">⚠️ Low Stock Alert</div>
        <p>The following items are running low and need to be restocked:</p>
        <ul>
          ${lowStockItems.map(item => `<li>${item.name} - Current: ${item.currentStock}, Reorder Level: ${item.reorderLevel}</li>`).join('')}
        </ul>
      </div>
      ` : ''}

      ${expiringItems.length > 0 ? `
      <div class="alert-section">
        <div class="section-title">⏰ Expiring Soon</div>
        <p>The following items are expiring within the next 3 months:</p>
        <ul>
          ${expiringItems.map(item => `<li>${item.name} - Expires: ${new Date(item.expiryDate).toLocaleDateString()}</li>`).join('')}
        </ul>
      </div>
      ` : ''}

      <div class="footer">
        <p>This inventory report was generated automatically.</p>
        <p>Generated on ${new Date().toLocaleString()}</p>
      </div>
    </body>
    </html>
    `;
  }
}

export default new PDFService();
