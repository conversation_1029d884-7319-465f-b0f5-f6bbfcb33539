import mongoose from 'mongoose';
import { io } from '../index.js';

// Get existing models
const getModels = () => {
  try {
    return {
      Notification: mongoose.model('Notification'),
      User: mongoose.model('User'),
      Patient: mongoose.model('Patient'),
      Appointment: mongoose.model('Appointment'),
      LabTest: mongoose.model('LabTest'),
      Bill: mongoose.model('Bill')
    };
  } catch (error) {
    console.error('Error getting models:', error);
    return {};
  }
};

// Notification types and their configurations
const NOTIFICATION_TYPES = {
  PATIENT_REGISTERED: {
    title: 'New Patient Registered',
    type: 'info',
    priority: 'medium',
    category: 'patient'
  },
  APPOINTMENT_SCHEDULED: {
    title: 'Appointment Scheduled',
    type: 'appointment',
    priority: 'medium',
    category: 'appointment'
  },
  APPOINTMENT_CANCELLED: {
    title: 'Appointment Cancelled',
    type: 'warning',
    priority: 'high',
    category: 'appointment'
  },
  LAB_RESULT_READY: {
    title: 'Lab Result Available',
    type: 'success',
    priority: 'high',
    category: 'laboratory'
  },
  PAYMENT_RECEIVED: {
    title: 'Payment Received',
    type: 'success',
    priority: 'medium',
    category: 'financial'
  },
  PAYMENT_OVERDUE: {
    title: 'Payment Overdue',
    type: 'error',
    priority: 'high',
    category: 'financial'
  },
  SYSTEM_ALERT: {
    title: 'System Alert',
    type: 'system',
    priority: 'urgent',
    category: 'system'
  },
  LOW_STOCK_ALERT: {
    title: 'Low Stock Alert',
    type: 'warning',
    priority: 'high',
    category: 'inventory'
  }
};

// Real-time notification service
class NotificationService {
  constructor() {
    this.connectedUsers = new Map(); // userId -> socketId mapping
  }

  // Register user connection
  registerUser(userId, socketId) {
    this.connectedUsers.set(userId, socketId);
    console.log(`User ${userId} connected with socket ${socketId}`);
  }

  // Unregister user connection
  unregisterUser(userId) {
    this.connectedUsers.delete(userId);
    console.log(`User ${userId} disconnected`);
  }

  // Create and send notification
  async createNotification(notificationType, recipientId, data = {}) {
    try {
      const models = getModels();
      if (!models.Notification) {
        console.error('Notification model not available');
        return null;
      }

      const config = NOTIFICATION_TYPES[notificationType];
      if (!config) {
        console.error(`Unknown notification type: ${notificationType}`);
        return null;
      }

      // Create notification in database
      const notificationData = {
        recipient: recipientId,
        title: config.title,
        message: this.generateMessage(notificationType, data),
        type: config.type,
        priority: config.priority,
        category: config.category,
        data: data,
        actionUrl: data.actionUrl || null
      };

      const notification = await models.Notification.create(notificationData);
      await notification.populate('sender', 'firstName lastName role');

      // Send real-time notification
      this.sendRealTimeNotification(recipientId, notification);

      // Send to all admins for certain types
      if (['SYSTEM_ALERT', 'LOW_STOCK_ALERT'].includes(notificationType)) {
        await this.sendToAllAdmins(notification);
      }

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      return null;
    }
  }

  // Send real-time notification via Socket.IO
  sendRealTimeNotification(userId, notification) {
    const socketId = this.connectedUsers.get(userId.toString());
    if (socketId && io) {
      io.to(socketId).emit('notification', {
        id: notification._id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        category: notification.category,
        data: notification.data,
        createdAt: notification.createdAt,
        actionUrl: notification.actionUrl
      });
      console.log(`Real-time notification sent to user ${userId}`);
    }
  }

  // Send notification to all admins
  async sendToAllAdmins(notification) {
    try {
      const models = getModels();
      if (!models.User) return;

      // Find all admin users
      const adminRole = await models.Role?.findOne({ name: 'Administrator' });
      if (!adminRole) return;

      const adminUsers = await models.User.find({ 
        role: adminRole._id, 
        isActive: true 
      }).select('_id');

      // Send to each admin
      for (const admin of adminUsers) {
        if (admin._id.toString() !== notification.recipient?.toString()) {
          await this.createNotification('SYSTEM_ALERT', admin._id, notification.data);
        }
      }
    } catch (error) {
      console.error('Error sending to admins:', error);
    }
  }

  // Generate message based on notification type and data
  generateMessage(notificationType, data) {
    switch (notificationType) {
      case 'PATIENT_REGISTERED':
        return `New patient ${data.patientName} (ID: ${data.patientId}) has been registered.`;
      
      case 'APPOINTMENT_SCHEDULED':
        return `Appointment scheduled for ${data.patientName} with Dr. ${data.doctorName} on ${data.appointmentDate}.`;
      
      case 'APPOINTMENT_CANCELLED':
        return `Appointment for ${data.patientName} with Dr. ${data.doctorName} has been cancelled.`;
      
      case 'LAB_RESULT_READY':
        return `Lab result for ${data.testName} is ready for patient ${data.patientName}.`;
      
      case 'PAYMENT_RECEIVED':
        return `Payment of $${data.amount} received from ${data.patientName}.`;
      
      case 'PAYMENT_OVERDUE':
        return `Payment of $${data.amount} is overdue for ${data.patientName}.`;
      
      case 'SYSTEM_ALERT':
        return data.message || 'System alert notification.';
      
      case 'LOW_STOCK_ALERT':
        return `Low stock alert: ${data.itemName} has only ${data.currentStock} units remaining.`;
      
      default:
        return 'New notification received.';
    }
  }

  // Broadcast to all connected users
  broadcastToAll(notification) {
    if (io) {
      io.emit('broadcast_notification', notification);
      console.log('Broadcast notification sent to all users');
    }
  }

  // Send notification to specific room/department
  sendToRoom(room, notification) {
    if (io) {
      io.to(room).emit('room_notification', notification);
      console.log(`Notification sent to room: ${room}`);
    }
  }

  // Get connected users count
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  // Get all connected users
  getConnectedUsers() {
    return Array.from(this.connectedUsers.keys());
  }
}

// Create singleton instance
const notificationService = new NotificationService();

// Helper functions for common notification scenarios
export const notifyPatientRegistered = async (patientData) => {
  return await notificationService.createNotification('PATIENT_REGISTERED', patientData.assignedDoctor, {
    patientName: `${patientData.firstName} ${patientData.lastName}`,
    patientId: patientData.patientId,
    actionUrl: `/patients/${patientData._id}`
  });
};

export const notifyAppointmentScheduled = async (appointmentData) => {
  // Notify doctor
  await notificationService.createNotification('APPOINTMENT_SCHEDULED', appointmentData.doctor._id, {
    patientName: `${appointmentData.patient.firstName} ${appointmentData.patient.lastName}`,
    doctorName: `${appointmentData.doctor.firstName} ${appointmentData.doctor.lastName}`,
    appointmentDate: appointmentData.appointmentDate,
    actionUrl: `/appointments/${appointmentData._id}`
  });

  // Notify patient if they have a user account
  if (appointmentData.patient.userId) {
    await notificationService.createNotification('APPOINTMENT_SCHEDULED', appointmentData.patient.userId, {
      patientName: `${appointmentData.patient.firstName} ${appointmentData.patient.lastName}`,
      doctorName: `${appointmentData.doctor.firstName} ${appointmentData.doctor.lastName}`,
      appointmentDate: appointmentData.appointmentDate,
      actionUrl: `/appointments/${appointmentData._id}`
    });
  }
};

export const notifyLabResultReady = async (labTestData) => {
  // Notify ordering doctor
  await notificationService.createNotification('LAB_RESULT_READY', labTestData.orderedBy._id, {
    testName: labTestData.testName,
    patientName: `${labTestData.patient.firstName} ${labTestData.patient.lastName}`,
    actionUrl: `/laboratory/${labTestData._id}`
  });

  // Notify patient if they have a user account
  if (labTestData.patient.userId) {
    await notificationService.createNotification('LAB_RESULT_READY', labTestData.patient.userId, {
      testName: labTestData.testName,
      patientName: `${labTestData.patient.firstName} ${labTestData.patient.lastName}`,
      actionUrl: `/laboratory/${labTestData._id}`
    });
  }
};

export const notifyPaymentReceived = async (paymentData) => {
  // Notify billing department
  await notificationService.createNotification('PAYMENT_RECEIVED', paymentData.processedBy, {
    amount: paymentData.amount,
    patientName: `${paymentData.patient.firstName} ${paymentData.patient.lastName}`,
    actionUrl: `/financial/bills/${paymentData.billId}`
  });
};

export const notifyLowStock = async (inventoryData) => {
  // This will automatically notify all admins
  await notificationService.createNotification('LOW_STOCK_ALERT', inventoryData.managerId || 'system', {
    itemName: inventoryData.itemName,
    currentStock: inventoryData.currentStock,
    reorderLevel: inventoryData.reorderLevel,
    actionUrl: `/pharmacy/inventory/${inventoryData._id}`
  });
};

export default notificationService;
